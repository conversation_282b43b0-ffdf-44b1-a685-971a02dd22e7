"""
URL configuration for biometrics app.
"""

from django.urls import path
from . import views

urlpatterns = [
    # Health check (no auth required)
    path('health/', views.health_check, name='health_check'),
    
    # Device management endpoints
    path('device-status/', views.device_status, name='device_status'),
    path('initialize-device/', views.initialize_device, name='initialize_device'),
    
    # Fingerprint capture and processing
    path('capture/', views.capture_fingerprint, name='capture_fingerprint'),
    path('process/', views.process_fingerprint, name='process_fingerprint'),
    
    # Quality validation and matching
    path('validate-quality/', views.validate_quality, name='validate_quality'),
    path('match/', views.match_fingerprints, name='match_fingerprints'),
    
    # Duplicate detection
    path('check-duplicates/', views.check_duplicates, name='check_duplicates'),
    path('fraud-prevention/check/', views.fraud_prevention_check, name='fraud_prevention_check'),
    path('test-duplicate-detection/', views.test_duplicate_detection, name='test_duplicate_detection'),
    path('debug-fingerprint-data/', views.debug_fingerprint_data, name='debug_fingerprint_data'),
    
    # Statistics
    path('stats/', views.biometric_stats, name='biometric_stats'),
]
