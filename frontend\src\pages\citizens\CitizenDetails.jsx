import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import { usePermissions } from '../../hooks/usePermissions';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  Button,
  Chip,
  Tab,
  Tabs,
  Grid,
  Card,
  CardContent,
  Divider,
  IconButton,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Edit as EditIcon,
  Badge as BadgeIcon,
  Person as PersonIcon,
  Home as HomeIcon,
  People as FamilyIcon,
  Work as WorkIcon,
  Description as DocumentIcon,
  CreditCard as IdCardIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  ArrowBack as ArrowBackIcon,
  PersonAdd as PersonAddIcon,
  SwapH<PERSON>z as TransferIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import DocumentUpload from '../../components/documents/DocumentUpload';
import TransferRequestForm from '../../components/transfers/TransferRequestForm';
import ClearanceRequestForm from '../../components/clearances/ClearanceRequestForm';
import EthiopianDateDisplay from '../../components/common/EthiopianDateDisplay';

/**
 * Get tenant ID from JWT token
 */
const getTenantId = () => {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.tenant_id) {
      return user.tenant_id;
    }

    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      console.warn('No access token found in localStorage');
      return null;
    }

    const base64Url = accessToken.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const tokenData = JSON.parse(jsonPayload);
    return tokenData.tenant_id || null;
  } catch (error) {
    console.error('Error getting tenant ID:', error);
    return null;
  }
};

const CitizenDetails = () => {
  const { id, tenantId } = useParams(); // Support both /citizens/:id and /tenants/:tenantId/citizens/:id
  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useLocalization();
  const { hasPermission } = usePermissions();
  const [citizen, setCitizen] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);

  // Transfer state
  const [transferDialogOpen, setTransferDialogOpen] = useState(false);
  const [clearanceDialogOpen, setClearanceDialogOpen] = useState(false);

  // Family data state
  const [familyData, setFamilyData] = useState({
    spouse: null,
    parents: [],
    children: [],
    emergencyContacts: []
  });

  useEffect(() => {
    console.log('🔍 CitizenDetails useEffect - user object:', user);
    console.log('🔍 CitizenDetails useEffect - city_tenant_name:', user?.city_tenant_name);
    fetchCitizenDetails();
    fetchFamilyData();
  }, [id, user]);

  // Debug user permissions
  useEffect(() => {
    console.log('🔍 User permissions debug:', {
      user,
      userRole: user?.role,
      userTenant: user?.tenant,
      userTenantType: user?.tenant?.type,
      userTenantId: user?.tenant?.id,
      allUserProperties: Object.keys(user || {}),
      hasTransferPermission: hasPermission('transfer_citizens'),
      hasViewCitizens: hasPermission('view_citizens'),
      hasRegisterCitizens: hasPermission('register_citizens'),
      hasCreateIdcards: hasPermission('create_idcards'),
      hasApproveIdcards: hasPermission('approve_idcards')
    });
  }, [user, hasPermission]);

  const fetchCitizenDetails = async () => {
    try {
      setLoading(true);

      // Use tenant ID from URL params if available (cross-tenant navigation), otherwise use current user's tenant
      const effectiveTenantId = tenantId || getTenantId();
      if (!effectiveTenantId) {
        setError('No tenant context found. Please log in again.');
        setLoading(false);
        return;
      }

      console.log('🔍 Fetching citizen details for tenant:', effectiveTenantId, 'citizen:', id);

      const expandParams = 'religion,marital_status,nationality,city,subcity,kebele,citizen_status';
      const requestUrl = `/api/tenants/${effectiveTenantId}/citizens/${id}/`;

      console.log('🔍 Request URL:', requestUrl);
      console.log('🔍 Expand params:', expandParams);

      // Fetch citizen with expanded foreign key relationships
      const response = await axios.get(requestUrl, {
        params: {
          expand: expandParams
        }
      });

      console.log('🔍 Full request URL with params:', response.config.url);

      setCitizen(response.data);
      console.log('✅ Citizen details fetched:', response.data);
      console.log('📊 Citizen data structure:', {
        religion: response.data.religion,
        marital_status: response.data.marital_status,
        nationality: response.data.nationality,
        city: response.data.city,
        subcity: response.data.subcity,
        kebele: response.data.kebele,
        ketena: response.data.ketena, // Add ketena to debug
        ketena_name: response.data.ketena_name, // Add ketena_name to debug
        phone: response.data.phone,
        phone_number: response.data.phone_number,
        blood_type: response.data.blood_type,
        date_of_birth: response.data.date_of_birth,
        gender: response.data.gender,
        first_name_am: response.data.first_name_am,
        middle_name_am: response.data.middle_name_am,
        last_name_am: response.data.last_name_am,
        // Debug emergency contacts
        emergency_contacts: response.data.emergency_contacts,
        // Debug all amharic fields
        amharicDebug: {
          first_name_am: response.data.first_name_am,
          middle_name_am: response.data.middle_name_am,
          last_name_am: response.data.last_name_am,
          hasAnyAmharic: !!(response.data.first_name_am || response.data.middle_name_am || response.data.last_name_am)
        },
        // Debug employment fields
        employmentDebug: {
          employment: response.data.employment,
          employee_type: response.data.employee_type,
          organization_name: response.data.organization_name,
          hasAnyEmployment: !!(response.data.employment || response.data.employee_type || response.data.organization_name)
        }
      });
    } catch (error) {
      console.error('Failed to fetch citizen details:', error);
      setError('Failed to load citizen details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchFamilyData = async () => {
    try {
      // Use tenant ID from URL params if available (cross-tenant navigation), otherwise use current user's tenant
      const effectiveTenantId = tenantId || getTenantId();
      if (!effectiveTenantId) return;

      console.log('🔍 Fetching family data for citizen:', id);

      // Fetch family data in parallel
      const [spouseRes, parentsRes, childrenRes, emergencyRes] = await Promise.allSettled([
        axios.get(`/api/tenants/${effectiveTenantId}/citizens/${id}/spouse/`),
        axios.get(`/api/tenants/${effectiveTenantId}/citizens/${id}/parents/`),
        axios.get(`/api/tenants/${effectiveTenantId}/citizens/${id}/children/`),
        axios.get(`/api/tenants/${effectiveTenantId}/citizens/${id}/emergency-contacts/`)
      ]);

      setFamilyData({
        spouse: spouseRes.status === 'fulfilled' ? spouseRes.value.data : null,
        parents: parentsRes.status === 'fulfilled' ? parentsRes.value.data : [],
        children: childrenRes.status === 'fulfilled' ? childrenRes.value.data : [],
        emergencyContacts: emergencyRes.status === 'fulfilled' ? emergencyRes.value.data : []
      });

      console.log('✅ Family data fetched:', {
        spouse: spouseRes.status === 'fulfilled' ? spouseRes.value.data : null,
        parents: parentsRes.status === 'fulfilled' ? parentsRes.value.data?.length || 0 : 0,
        children: childrenRes.status === 'fulfilled' ? childrenRes.value.data?.length || 0 : 0,
        emergencyContacts: emergencyRes.status === 'fulfilled' ? emergencyRes.value.data?.length || 0 : 0
      });
    } catch (error) {
      console.error('Failed to fetch family data:', error);
      // Don't show error for family data, just log it
    }
  };

  const calculateAge = (birthDate) => {
    if (!birthDate) return 'N/A';
    try {
      const today = new Date();
      const birth = new Date(birthDate);

      // Check if the date is valid
      if (isNaN(birth.getTime())) return 'N/A';

      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }

      return age;
    } catch (error) {
      console.error('Error calculating age:', error);
      return 'N/A';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) return 'N/A';

      // For now, return Gregorian format with Ethiopian label
      // TODO: Implement proper Ethiopian date conversion
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }) + ' (Gregorian)';
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  };

  const getGenderIcon = (gender) => {
    switch (gender?.toLowerCase()) {
      case 'male':
        return <MaleIcon sx={{ color: 'info.main' }} />;
      case 'female':
        return <FemaleIcon sx={{ color: 'secondary.main' }} />;
      default:
        return <PersonIcon />;
    }
  };

  const getDisplayValue = (value, fallback = 'Not specified') => {
    if (!value) return fallback;
    if (typeof value === 'object' && value.name) return value.name;
    if (typeof value === 'string' || typeof value === 'number') return value;
    return fallback;
  };

  const getCityName = () => {
    console.log('🔍 getCityName called with user:', {
      user: user,
      city_tenant_name: user?.city_tenant_name,
      tenant_type: user?.tenant_type,
      tenant_name: user?.tenant_name,
      parent_tenant_name: user?.parent_tenant_name
    });

    // Priority 1: Use city information from access token (works for all tenant types in hierarchy)
    // This handles City -> Subcity -> Kebele hierarchy automatically
    if (user?.city_tenant_name) {
      console.log('✅ Using city_tenant_name:', user.city_tenant_name);
      return user.city_tenant_name;
    }

    // Priority 2: Try to get city from citizen data (if explicitly stored)
    if (citizen.city && typeof citizen.city === 'object' && citizen.city.name) {
      console.log('✅ Using citizen.city.name:', citizen.city.name);
      return citizen.city.name;
    }
    if (citizen.city && typeof citizen.city === 'string') {
      console.log('✅ Using citizen.city string:', citizen.city);
      return citizen.city;
    }

    // Priority 3: Fallback based on tenant type (legacy support)
    if (user?.tenant_type === 'city') {
      // For city tenants, use their own name
      console.log('✅ Using city tenant name:', user?.tenant_name);
      return user?.tenant_name || 'Not specified';
    }

    if (user?.tenant_type === 'subcity') {
      // For subcity tenants, the parent should be the city
      console.log('✅ Using subcity parent name:', user?.parent_tenant_name);
      return user?.parent_tenant_name || 'Not specified';
    }

    if (user?.tenant_type === 'kebele') {
      // For kebele tenants, this should be handled by city_tenant_name above
      // This is a fallback if token doesn't have city info
      console.log('⚠️ Using kebele fallback: Fasil Castle');
      return 'Fasil Castle'; // Fallback for legacy tokens
    }

    console.log('❌ No city found, returning Not specified');
    return 'Not specified';
  };

  // Check if user is in a kebele tenant (where editing is allowed)
  const isInKebeleTenant = () => {
    // If we're viewing cross-tenant (tenantId in URL), we're in subcity viewing kebele data - no editing
    if (tenantId) return false;

    // Check if current user's tenant is a kebele type
    return user?.tenant_type === 'kebele' || user?.role === 'clerk' || user?.role === 'kebele_leader';
  };

  // Check if user can initiate transfers (only kebele leaders)
  const canInitiateTransfer = () => {
    // Allow transfers if user is a kebele leader and either:
    // 1. No tenantId in URL (viewing own tenant data), OR
    // 2. tenantId in URL matches user's tenant (viewing own tenant via tenant-specific URL)
    const isOwnTenant = !tenantId || (tenantId && parseInt(tenantId) === user?.tenant?.id);
    const isKebeleTenant = user?.tenant?.type === 'kebele';
    const isKebeleLeader = user?.role === 'kebele_leader';

    console.log('🔍 Transfer button debug (fixed):', {
      tenantId,
      userTenantId: user?.tenant?.id,
      isOwnTenant,
      isKebeleTenant,
      isKebeleLeader,
      userRole: user?.role,
      userTenantType: user?.tenant?.type,
      hasTransferPermission: hasPermission('transfer_citizens'),
      citizenIsActive: citizen?.is_active,
      result: isOwnTenant && isKebeleTenant && isKebeleLeader
    });

    return isOwnTenant && isKebeleTenant && isKebeleLeader;
  };

  // Handle transfer success
  const handleTransferSuccess = (transferData) => {
    console.log('✅ Transfer request created successfully:', transferData);
    // You could show a success message or navigate to transfers page
    alert(`Transfer request ${transferData.transfer_id} created successfully!`);
  };

  // Handle clearance success
  const handleClearanceSuccess = (clearanceData) => {
    console.log('✅ Clearance letter generated successfully:', clearanceData);
    // Show success message
    alert(`Clearance letter ${clearanceData.clearance_id} generated successfully!`);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/citizens')}
        >
          Back to Citizens List
        </Button>
      </Box>
    );
  }

  if (!citizen) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Citizen not found.
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => {
            // If we came from cross-tenant navigation, go back to appropriate list
            if (tenantId) {
              navigate('/citizens/all-kebeles');
            } else {
              navigate('/citizens');
            }
          }}
        >
          Back to Citizens List
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => {
            // If we came from cross-tenant navigation, go back to appropriate list
            if (tenantId) {
              navigate('/citizens/all-kebeles');
            } else {
              navigate('/citizens');
            }
          }}
          sx={{ mb: 2 }}
        >
          Back to Citizens List
        </Button>
      </Box>

      {/* Citizen Profile Header */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 3 }}>
          <Avatar
            src={citizen.photo}
            sx={{
              width: 120,
              height: 120,
              bgcolor: 'grey.300',
              fontSize: '3rem',
              fontWeight: 'bold'
            }}
          >
            {citizen.first_name?.charAt(0)?.toUpperCase() || 'U'}
          </Avatar>

          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
              {citizen.full_name || `${citizen.first_name || ''} ${citizen.middle_name || ''} ${citizen.last_name || ''}`.trim()}
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
              {citizen.first_name_am || citizen.middle_name_am || citizen.last_name_am ?
                `${citizen.first_name_am || ''} ${citizen.middle_name_am || ''} ${citizen.last_name_am || ''}`.trim() :
                t('no_amharic_name', 'No Amharic name')
              }
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
              <Chip
                icon={<BadgeIcon />}
                label={`ID: ${citizen.digital_id || citizen.citizen_id || 'N/A'}`}
                color="success"
                variant="filled"
              />

              <Chip
                label={t('resident', 'Resident')}
                color="success"
                size="small"
              />
            </Box>
          </Box>

          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' },
            gap: 2,
            mt: 2
          }}>
            {/* ID Card Registration - Only for kebele tenants */}
            {!citizen.has_id_card && isInKebeleTenant() && hasPermission('create_idcards') && (
              <Button
                variant="contained"
                startIcon={<BadgeIcon />}
                onClick={() => navigate(`/citizens/${citizen.id}/idcards/create`)}
                size="small"
                sx={{
                  bgcolor: 'success.main',
                  '&:hover': { bgcolor: 'success.dark' },
                  height: '36px',
                  borderRadius: 1,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                  px: 2
                }}
              >
                ID Card
              </Button>
            )}

            {/* View ID Card - Only for kebele tenants */}
            {citizen.has_id_card && isInKebeleTenant() && (
              <Button
                variant="contained"
                startIcon={<BadgeIcon />}
                onClick={async () => {
                  try {
                    const effectiveTenantId = tenantId || getTenantId();
                    // Fetch the citizen's ID card to get the card ID
                    const response = await axios.get(`/api/tenants/${effectiveTenantId}/citizens/${citizen.id}/idcards/`);
                    const idCards = response.data;

                    if (idCards && idCards.length > 0) {
                      // Navigate to the first (most recent) ID card detail page
                      const latestCard = idCards[0];
                      navigate(`/idcards/${latestCard.id}`);
                    } else {
                      // Fallback to ID cards list if no cards found
                      navigate(`/idcards?citizen=${citizen.id}`);
                    }
                  } catch (error) {
                    console.error('Error fetching citizen ID cards:', error);
                    // Fallback to ID cards list
                    navigate(`/idcards?citizen=${citizen.id}`);
                  }
                }}
                size="small"
                sx={{
                  bgcolor: 'info.main',
                  '&:hover': { bgcolor: 'info.dark' },
                  height: '36px',
                  borderRadius: 1,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                  px: 2
                }}
              >
                ID Card
              </Button>
            )}

            {/* Transfer Citizen - Only for kebele leaders with transfer permission */}
            {canInitiateTransfer() && hasPermission('transfer_citizens') && citizen.is_active !== false && (
              <Button
                variant="contained"
                startIcon={<TransferIcon />}
                onClick={() => setTransferDialogOpen(true)}
                size="small"
                sx={{
                  bgcolor: 'warning.main',
                  '&:hover': { bgcolor: 'warning.dark' },
                  height: '36px',
                  borderRadius: 1,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                  px: 2
                }}
              >
                Transfer
              </Button>
            )}

            {/* Clearance Letter - Only for kebele leaders with transfer permission */}
            {canInitiateTransfer() && hasPermission('transfer_citizens') && citizen.is_active !== false && (
              <Button
                variant="contained"
                startIcon={<SendIcon />}
                onClick={() => setClearanceDialogOpen(true)}
                size="small"
                sx={{
                  bgcolor: 'secondary.main',
                  '&:hover': { bgcolor: 'secondary.dark' },
                  height: '36px',
                  borderRadius: 1,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                  px: 2
                }}
              >
                Clearance
              </Button>
            )}

            {/* Edit Citizen - Only for kebele tenants */}
            {isInKebeleTenant() && hasPermission('register_citizens') && (
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
                onClick={() => navigate(`/citizens/${citizen.id}/edit`)}
                size="small"
                sx={{
                  borderColor: 'success.main',
                  color: 'success.main',
                  '&:hover': {
                    borderColor: 'success.dark',
                    bgcolor: 'success.light',
                    color: 'success.dark'
                  },
                  height: '36px',
                  borderRadius: 1,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                  px: 2
                }}
              >
                Edit
              </Button>
            )}


          </Box>
        </Box>
      </Paper>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab icon={<PersonIcon />} label={t('personal_info', 'Personal Info')} />
          <Tab icon={<HomeIcon />} label={t('contact_location', 'Contact & Location')} />
          <Tab icon={<FamilyIcon />} label={t('family', 'Family')} />
          <Tab icon={<WorkIcon />} label={t('employment', 'Employment')} />
          <Tab icon={<DocumentIcon />} label={t('documents', 'Documents')} />
          <Tab icon={<IdCardIcon />} label={t('id_cards', 'ID Cards')} />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {activeTab === 0 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <PersonIcon color="success" />
            {t('personal_information', 'Personal Information')}
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                {getGenderIcon(citizen.gender)}
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Gender</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.gender || 'Not specified'}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CalendarIcon sx={{ color: 'success.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Date of Birth (Ethiopian Calendar)</Typography>
                  <EthiopianDateDisplay
                    date={citizen.date_of_birth}
                    format="DD MMM YYYY"
                    language="en"
                    showGregorian={true}
                    variant="body1"
                    fontWeight="medium"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Age: {calculateAge(citizen.date_of_birth)} years
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon sx={{ color: 'info.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Nationality</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {getDisplayValue(citizen.nationality, 'Ethiopian')}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon sx={{ color: 'secondary.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Religion</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {getDisplayValue(citizen.religion)}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <FamilyIcon sx={{ color: 'warning.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Marital Status</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {getDisplayValue(citizen.marital_status)}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon sx={{ color: 'primary.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Citizen Status</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {getDisplayValue(citizen.citizen_status || citizen.status, 'Active')}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon sx={{ color: 'error.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Blood Type</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.blood_type || 'Not specified'}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon sx={{ color: 'warning.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Disability Status</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {getDisplayValue(citizen.disability, 'None')}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {activeTab === 1 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <HomeIcon color="success" />
            Contact & Location Information
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PhoneIcon sx={{ color: 'success.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Phone Number</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.phone || citizen.phone_number || 'Not provided'}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <EmailIcon sx={{ color: 'info.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Email</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.email || 'Not provided'}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <LocationIcon sx={{ color: 'warning.main', mt: 0.5 }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">House Number</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.house_number || 'Not provided'}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocationIcon sx={{ color: 'primary.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">City</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {getCityName()}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocationIcon sx={{ color: 'secondary.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Subcity</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {getDisplayValue(citizen.subcity)}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocationIcon sx={{ color: 'success.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Kebele</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {getDisplayValue(citizen.kebele)}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocationIcon sx={{ color: 'warning.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Ketena</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.ketena_name || getDisplayValue(citizen.ketena)}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {activeTab === 2 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <FamilyIcon color="success" />
            Family Information
          </Typography>

          <Grid container spacing={3}>
            {/* Spouse Information */}
            <Grid item xs={12} md={6}>
              <Box sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                p: 3,
                textAlign: 'center',
                minHeight: 300
              }}>
                <Chip
                  label="Spouse Information"
                  color="success"
                  sx={{ mb: 2, fontWeight: 'bold' }}
                />

                {familyData.spouse ? (
                  <Box>
                    <Avatar
                      src={familyData.spouse.photo}
                      sx={{ width: 60, height: 60, mx: 'auto', mb: 2 }}
                    >
                      {familyData.spouse.first_name?.charAt(0)}
                    </Avatar>
                    <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                      {`${familyData.spouse.first_name || ''} ${familyData.spouse.middle_name || ''} ${familyData.spouse.last_name || ''}`.trim()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Phone: {familyData.spouse.phone || 'Not provided'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Relationship: Spouse
                    </Typography>
                  </Box>
                ) : (
                  <Box>
                    <Box sx={{ mb: 3 }}>
                      <FamilyIcon sx={{ fontSize: 60, color: 'grey.400', mb: 2 }} />
                    </Box>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      No spouse information recorded.
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      {getDisplayValue(citizen?.marital_status)?.toLowerCase() === 'married' ?
                        'This citizen is marked as married but no spouse information has been added yet.' :
                        'Add spouse information when the citizen gets married.'
                      }
                    </Typography>
                  </Box>
                )}

                <Button
                  variant="outlined"
                  startIcon={<PersonAddIcon />}
                  color="success"
                  size="small"
                >
                  {familyData.spouse ? 'Edit Spouse Information' : 'Add Spouse Information'}
                </Button>
              </Box>
            </Grid>

            {/* Parents Information */}
            <Grid item xs={12} md={6}>
              <Box sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                p: 3,
                textAlign: 'center',
                minHeight: 300
              }}>
                <Chip
                  label="Parents Information"
                  color="success"
                  sx={{ mb: 2, fontWeight: 'bold' }}
                />

                {familyData.parents && familyData.parents.length > 0 ? (
                  <Box>
                    {familyData.parents.map((parent, index) => (
                      <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid', borderColor: 'grey.300', borderRadius: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {`${parent.first_name || ''} ${parent.middle_name || ''} ${parent.last_name || ''}`.trim()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {parent.gender === 'male' ? 'Father' : parent.gender === 'female' ? 'Mother' : (parent.relationship || 'Parent')}
                        </Typography>
                        {parent.phone && (
                          <Typography variant="body2" color="text.secondary">
                            Phone: {parent.phone}
                          </Typography>
                        )}
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Box>
                    <Box sx={{ mb: 3 }}>
                      <PersonIcon sx={{ fontSize: 60, color: 'grey.400', mb: 2 }} />
                    </Box>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      No parent information recorded.
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Adding parent information helps establish family relationships and can be useful for verification purposes.
                    </Typography>
                  </Box>
                )}

                <Button
                  variant="outlined"
                  startIcon={<PersonAddIcon />}
                  color="success"
                  size="small"
                >
                  {familyData.parents && familyData.parents.length > 0 ? 'Edit Parent Information' : 'Add Parent Information'}
                </Button>
              </Box>
            </Grid>

            {/* Children Information */}
            <Grid item xs={12} md={6}>
              <Box sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                p: 3,
                textAlign: 'center',
                minHeight: 300
              }}>
                <Chip
                  label="Children Information"
                  color="success"
                  sx={{ mb: 2, fontWeight: 'bold' }}
                />

                {familyData.children && familyData.children.length > 0 ? (
                  <Box>
                    {familyData.children.map((child, index) => (
                      <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid', borderColor: 'grey.300', borderRadius: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {`${child.first_name || ''} ${child.middle_name || ''} ${child.last_name || ''}`.trim()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Age: {child.date_of_birth ? calculateAge(child.date_of_birth) : (child.age || 'Unknown')} • {child.gender || 'Unknown'}
                        </Typography>
                        {child.is_resident && (
                          <Chip label="Resident" size="small" color="success" sx={{ mt: 1 }} />
                        )}
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Box>
                    <Box sx={{ mb: 3 }}>
                      <FamilyIcon sx={{ fontSize: 60, color: 'grey.400', mb: 2 }} />
                    </Box>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      No children information recorded.
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      You can add children information to establish family relationships.
                    </Typography>
                  </Box>
                )}

                <Button
                  variant="outlined"
                  startIcon={<PersonAddIcon />}
                  color="success"
                  size="small"
                >
                  {familyData.children && familyData.children.length > 0 ? 'Edit Children Information' : 'Add Children Information'}
                </Button>
              </Box>
            </Grid>

            {/* Emergency Contacts */}
            <Grid item xs={12} md={6}>
              <Box sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                p: 3,
                textAlign: 'center',
                minHeight: 300
              }}>
                <Chip
                  label="Emergency Contacts"
                  color="error"
                  sx={{ mb: 2, fontWeight: 'bold' }}
                />

                {familyData.emergencyContacts && familyData.emergencyContacts.length > 0 ? (
                  <Box>
                    {familyData.emergencyContacts.map((contact, index) => (
                      <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid', borderColor: 'grey.300', borderRadius: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {`${contact.first_name || ''} ${contact.middle_name || ''} ${contact.last_name || ''}`.trim()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {getDisplayValue(contact.relationship, 'Emergency Contact')}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Phone: {contact.phone || 'Not provided'}
                        </Typography>
                        {contact.email && (
                          <Typography variant="body2" color="text.secondary">
                            Email: {contact.email}
                          </Typography>
                        )}
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Box>
                    <Box sx={{ mb: 3 }}>
                      <PhoneIcon sx={{ fontSize: 60, color: 'grey.400', mb: 2 }} />
                    </Box>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      No emergency contacts recorded.
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Emergency contacts are important for reaching out to someone in case of an emergency.
                    </Typography>
                  </Box>
                )}

                <Button
                  variant="outlined"
                  startIcon={<PersonAddIcon />}
                  color="error"
                  size="small"
                >
                  {familyData.emergencyContacts && familyData.emergencyContacts.length > 0 ? 'Edit Emergency Contact' : 'Add Emergency Contact'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {activeTab === 3 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <WorkIcon color="success" />
            Employment Information
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <WorkIcon sx={{ color: 'primary.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Employment Status</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.employment || 'Not specified'}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <WorkIcon sx={{ color: 'secondary.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Employee Type</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.employee_type || 'Not specified'}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <WorkIcon sx={{ color: 'success.main' }} />
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" color="text.secondary">Organization Name</Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {citizen.organization_name || 'Not specified'}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            {/* Employment Summary Card */}
            {(citizen.employment || citizen.employee_type || citizen.organization_name) && (
              <Grid item xs={12}>
                <Box sx={{
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 2,
                  p: 3,
                  mt: 2,
                  backgroundColor: 'background.paper'
                }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, color: 'primary.main' }}>
                    Employment Summary
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    <strong>Status:</strong> {citizen.employment || 'Not specified'}
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    <strong>Type:</strong> {citizen.employee_type || 'Not specified'}
                  </Typography>
                  <Typography variant="body1">
                    <strong>Organization:</strong> {citizen.organization_name || 'Not specified'}
                  </Typography>
                </Box>
              </Grid>
            )}

            {/* Show message if no employment data */}
            {!citizen.employment && !citizen.employee_type && !citizen.organization_name && (
              <Grid item xs={12}>
                <Box sx={{
                  textAlign: 'center',
                  py: 4,
                  border: '1px dashed',
                  borderColor: 'divider',
                  borderRadius: 2,
                  backgroundColor: 'grey.50'
                }}>
                  <WorkIcon sx={{ fontSize: 60, color: 'grey.400', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
                    No employment information available
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Employment details can be added during citizen registration or updated later.
                  </Typography>
                </Box>
              </Grid>
            )}
          </Grid>
        </Paper>
      )}

      {activeTab === 4 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <DocumentIcon color="success" />
            Documents
          </Typography>

          <DocumentUpload
            citizenId={citizen.id}
            tenantId={tenantId || getTenantId()}
            initialDocuments={citizen.documents}
            onDocumentUploaded={() => {
              // Refresh citizen data to get updated documents
              fetchCitizenDetails();
            }}
          />
        </Paper>
      )}

      {activeTab === 5 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <IdCardIcon color="success" />
            ID Cards
          </Typography>

          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
            ID card information will be displayed here when available.
            This includes issued ID cards and their status.
          </Typography>
        </Paper>
      )}

      {/* Transfer Request Dialog */}
      <TransferRequestForm
        open={transferDialogOpen}
        onClose={() => setTransferDialogOpen(false)}
        citizen={citizen}
        onSuccess={handleTransferSuccess}
      />

      {/* Clearance Request Dialog */}
      <ClearanceRequestForm
        open={clearanceDialogOpen}
        onClose={() => setClearanceDialogOpen(false)}
        citizen={citizen}
        onSuccess={handleClearanceSuccess}
      />
    </Box>
  );
};

export default CitizenDetails;
