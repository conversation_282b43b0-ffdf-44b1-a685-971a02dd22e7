from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404
from django.db import connection
from django.db.models import Q, Count
from django.db.models.functions import TruncMonth
from django_tenants.utils import schema_context
from django.utils import timezone

from tenants.models import Tenant
from idcards.models import IDCard, IDCardTemplate, IDCardStatus
from idcards.serializers import IDCardSerializer, IDCardTemplateSerializer, IDCardApprovalSerializer
from common.permissions import CanManageIDCards, CanApproveIDCards, CanPrintIDCards
from tenants.permissions import CanManageTenants
from rest_framework import serializers
from citizens.models import Citizen
from users.models import User
from workflows.models import WorkflowLog, ApprovalAction


class CanManageIDCardsInTenantContext(permissions.BasePermission):
    """
    Custom permission for ID card management that allows cross-tenant access for subcity admins.
    Subcity admins can approve ID cards in kebele tenant contexts.
    """
    def has_permission(self, request, view):
        user = request.user

        print(f"🔍 PERMISSION CHECK:")
        print(f"  User: {user}")
        print(f"  User email: {getattr(user, 'email', 'No email')}")
        print(f"  User role: {getattr(user, 'role', 'No role')}")
        print(f"  Is authenticated: {user.is_authenticated if user else False}")
        print(f"  Is superuser: {getattr(user, 'is_superuser', False)}")
        print(f"  Request method: {request.method}")
        print(f"  View action: {getattr(view, 'action', 'No action')}")

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            print(f"❌ Permission denied: User not authenticated")
            return False

        # Superusers have full permission
        if user.is_superuser or user.role == 'superadmin':
            print(f"✅ Permission granted: Superuser/superadmin")
            return True

        # For safe methods (GET, HEAD, OPTIONS), all roles have permission
        if request.method in permissions.SAFE_METHODS:
            if user.role in ['city_admin', 'subcity_admin', 'kebele_admin', 'kebele_leader', 'clerk']:
                print(f"✅ Permission granted: Safe method for role {user.role}")
                return True
            else:
                print(f"❌ Permission denied: Role {user.role} not allowed for safe methods")

        # For unsafe methods, check specific permissions
        if view.action == 'create' and user.role == 'clerk':
            print(f"✅ Permission granted: Clerk can create")
            return True

        # Approval and printing actions - allow subcity admins to approve/print in kebele contexts
        if view.action in ['update_status', 'approval_action', 'print', 'list', 'retrieve']:
            if user.role in ['clerk', 'kebele_leader', 'kebele_admin', 'subcity_admin', 'city_admin', 'superadmin']:
                print(f"✅ Permission granted: Role {user.role} can perform {view.action}")
                return True
            else:
                print(f"❌ Permission denied: Role {user.role} cannot perform {view.action}")

        print(f"❌ Permission denied: No matching permission rule")
        return False

    def has_object_permission(self, request, view, obj):
        user = request.user

        # Superusers have full permission
        if user.is_superuser or user.role == 'superadmin':
            return True

        # Allow subcity admins to access ID cards in kebele tenant contexts for approval
        if user.role == 'subcity_admin' and view.action in ['approval_action', 'print']:
            return True

        # Standard permissions for other cases
        return True


class TenantSpecificIDCardSerializer(serializers.ModelSerializer):
    """
    Simple serializer for ID cards - let Django handle everything normally.
    """
    citizen_name = serializers.CharField(source='citizen.get_full_name', read_only=True)
    citizen_digital_id = serializers.CharField(source='citizen.digital_id', read_only=True)
    citizen_id = serializers.IntegerField(source='citizen.id', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    printed_by_username = serializers.CharField(source='printed_by.username', read_only=True)

    class Meta:
        model = IDCard
        fields = (
            'id', 'card_number', 'citizen', 'citizen_id', 'citizen_name', 'citizen_digital_id',
            'template', 'issue_date', 'expiry_date', 'status', 'qr_code', 'uuid',
            'has_kebele_pattern', 'has_subcity_pattern',  # Added security pattern fields
            'created_by', 'created_by_username', 'printed_by', 'printed_by_username', 'printed_at',
            'subcity_approved_at', 'created_at', 'updated_at', 'pdf_file'
        )
        read_only_fields = (
            'card_number', 'qr_code', 'uuid', 'created_by', 'printed_by', 'printed_at',
            'subcity_approved_at', 'created_at', 'updated_at', 'pdf_file'
        )

    def to_representation(self, instance):
        """
        Custom representation to handle citizen data properly.
        """
        data = super().to_representation(instance)

        # Ensure citizen data is properly included
        if instance.citizen:
            data['citizen_id'] = instance.citizen.id
            data['citizen_name'] = instance.citizen.get_full_name()
            data['citizen_digital_id'] = instance.citizen.digital_id
        else:
            data['citizen_id'] = None
            data['citizen_name'] = 'Unknown Citizen'
            data['citizen_digital_id'] = 'N/A'

        return data


class TenantSpecificIDCardViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing ID cards within a specific tenant schema.

    This viewset handles ID card operations within the context of a specific tenant,
    ensuring proper schema isolation and tenant-specific data access.
    """
    serializer_class = TenantSpecificIDCardSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageIDCardsInTenantContext]

    def get_tenant(self):
        """Get the tenant from URL parameter, bypassing any middleware issues."""
        tenant_id = self.kwargs.get('tenant_id')
        print(f"🔍 get_tenant called with tenant_id: {tenant_id}")
        print(f"🔍 kwargs: {self.kwargs}")

        try:
            # Direct database lookup to bypass any middleware issues
            tenant = Tenant.objects.get(id=tenant_id)
            print(f"✅ Found tenant via direct lookup: {tenant.name} (type: {type(tenant)})")
            print(f"✅ Tenant schema: {tenant.schema_name}")
            return tenant
        except Tenant.DoesNotExist:
            print(f"❌ Tenant {tenant_id} does not exist in database")
            from django.http import Http404
            raise Http404(f"Tenant {tenant_id} not found")
        except Exception as e:
            print(f"❌ Error getting tenant {tenant_id}: {str(e)}")
            print(f"❌ Error type: {type(e)}")
            raise

    def get_queryset(self):
        """Get ID cards within the tenant's schema context."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen

            # By default, only show ID cards for active citizens (hide transferred citizens)
            show_transferred = self.request.query_params.get('show_transferred', 'false').lower() == 'true'
            if show_transferred:
                # Show all ID cards including those for transferred citizens
                queryset = IDCard.objects.all()
            else:
                # Only show ID cards for active citizens (filter out transferred citizens)
                active_citizen_ids = Citizen.objects.filter(is_active=True).values_list('id', flat=True)
                queryset = IDCard.objects.filter(citizen_id__in=active_citizen_ids)

            # Apply role-based filtering
            user = self.request.user
            if user.role == 'clerk':
                # Clerks can only see ID cards they created
                queryset = queryset.filter(created_by=user)
            elif user.role == 'kebele_leader':
                # Kebele leaders can see ID cards pending approval AND those they've already approved (for reporting)
                queryset = queryset.filter(
                    status__in=[IDCardStatus.PENDING_APPROVAL, IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                )
            elif user.role in ['kebele_admin', 'subcity_admin', 'city_admin', 'superadmin'] or user.is_superuser:
                # Admins and superusers can see all ID cards
                pass  # No filtering, show all

            return queryset

    def create(self, request, *args, **kwargs):
        """Create ID card within the tenant's schema context."""
        print(f"🚀 ID CARD CREATE REQUEST RECEIVED!")
        print(f"🔍 Request method: {request.method}")
        print(f"🔍 Request path: {request.path}")
        print(f"🔍 Request data: {request.data}")
        print(f"🔍 Request user: {request.user}")

        tenant = self.get_tenant()

        # Handle both real Tenant objects and FakeTenant objects
        tenant_name = getattr(tenant, 'name', 'Unknown')
        tenant_schema = getattr(tenant, 'schema_name', 'Unknown')
        print(f"🔍 Creating ID card for tenant: {tenant_name} ({tenant_schema})")
        print(f"🔍 Tenant type: {type(tenant)}")

        with schema_context(tenant_schema):
            print(f"🔍 Inside schema context: {tenant_schema}")

            # Check current schema
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT current_schema()")
                current_schema = cursor.fetchone()[0]
                print(f"🔍 Current PostgreSQL schema: {current_schema}")

            # Check what citizen ID 4 is in this schema
            citizen_id = request.data.get('citizen')
            if citizen_id:
                print(f"🔍 Looking for citizen ID {citizen_id} in schema {current_schema}")
                try:
                    from citizens.models import Citizen
                    citizen = Citizen.objects.get(pk=citizen_id)
                    print(f"✅ Found citizen: {citizen.get_full_name()} (Digital: {citizen.digital_id})")
                except Citizen.DoesNotExist:
                    print(f"❌ Citizen ID {citizen_id} not found in schema {current_schema}")

            # Let Django handle the validation normally - just like citizen registration
            # Get the serializer and validate within the tenant context
            serializer = self.get_serializer(data=request.data)
            print(f"🔍 Serializer validation starting...")

            try:
                serializer.is_valid(raise_exception=True)
                print(f"✅ Serializer validation passed")

                # Set the created_by field to the current user
                instance = serializer.save(created_by=request.user)
                print(f"✅ ID card created successfully: {instance.card_number}")
                print(f"✅ ID card citizen: {instance.citizen.get_full_name()} (ID: {instance.citizen.id})")

                headers = self.get_success_headers(serializer.data)
                return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

            except Exception as e:
                print(f"❌ Error during ID card creation: {str(e)}")
                print(f"❌ Error type: {type(e)}")
                if hasattr(e, 'detail'):
                    print(f"❌ Error detail: {e.detail}")
                raise

    def perform_create(self, serializer):
        """Create ID card within the tenant's schema context."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            # Set the created_by field to the current user
            serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        """Update ID card within the tenant's schema context."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            serializer.save()

    def perform_destroy(self, instance):
        """Delete ID card within the tenant's schema context."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            instance.delete()

    def retrieve(self, request, *args, **kwargs):
        """Retrieve ID card within the tenant's schema context."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(serializer.data)

    def list(self, request, *args, **kwargs):
        """List ID cards within the tenant's schema context."""
        print(f"🔍 LIST ID CARDS REQUEST:")
        print(f"  User: {request.user.email}")
        print(f"  Role: {request.user.role}")
        print(f"  Is authenticated: {request.user.is_authenticated}")
        print(f"  Tenant ID: {kwargs.get('tenant_id')}")
        print(f"  Query params: {request.GET}")

        tenant = self.get_tenant()
        print(f"  Tenant: {tenant.name} ({tenant.schema_name})")

        with schema_context(tenant.schema_name):
            queryset = self.filter_queryset(self.get_queryset())
            print(f"  Queryset count: {queryset.count()}")

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    @action(detail=True, methods=['patch'])
    def update_status(self, request, pk=None, tenant_id=None):
        """Update ID card status (approve, reject, etc.)."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            id_card = self.get_object()
            new_status = request.data.get('status')

            if new_status not in [choice[0] for choice in IDCardStatus.choices]:
                return Response(
                    {'error': 'Invalid status'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Update status and set appropriate user fields
            id_card.status = new_status
            if new_status == 'approved':
                id_card.approved_by = request.user
            elif new_status == 'issued':
                id_card.issued_by = request.user

            id_card.save()

            serializer = self.get_serializer(id_card)
            return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def approval_action(self, request, pk=None, tenant_id=None):
        """Handle ID card approval workflow actions in tenant context."""
        print(f"🔍 approval_action called")
        print(f"  User: {request.user.email}")
        print(f"  Role: {request.user.role}")
        print(f"  Is superuser: {request.user.is_superuser}")
        print(f"  Tenant ID: {tenant_id}")
        print(f"  ID Card ID: {pk}")
        print(f"  Request data: {request.data}")

        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            id_card = self.get_object()
            print(f"  ID Card status: {id_card.status}")
            print(f"  ID Card citizen: {id_card.citizen.get_full_name()}")

            serializer = IDCardApprovalSerializer(data=request.data)

            if serializer.is_valid():
                action = serializer.validated_data['action']
                comment = serializer.validated_data.get('comment', '')

                print(f"  Action: {action}")
                print(f"  Comment: {comment}")

                old_status = id_card.status
                workflow_action = None  # Initialize to avoid UnboundLocalError

                print(f"🔍 DEBUG: About to process action '{action}' for ID card {id_card.id}")
                print(f"🔍 DEBUG: Current ID card status: {id_card.status}")
                print(f"🔍 DEBUG: User role: {request.user.role}")

                if action == 'submit_for_approval':
                    # Clerks and superadmins can submit for approval, and only draft cards
                    if request.user.role not in ['clerk', 'superadmin'] and not request.user.is_superuser:
                        return Response(
                            {'error': 'Only clerks and superadmins can submit ID cards for approval'},
                            status=status.HTTP_403_FORBIDDEN
                        )
                    if id_card.status != IDCardStatus.DRAFT:
                        return Response(
                            {'error': 'Only draft ID cards can be submitted for approval'},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    id_card.status = IDCardStatus.PENDING_APPROVAL
                    id_card.submitted_for_approval_at = timezone.now()
                    workflow_action = ApprovalAction.SUBMIT

                elif action == 'kebele_approve':
                    # Only kebele leaders can approve at kebele level
                    if request.user.role not in ['kebele_leader', 'superadmin'] and not request.user.is_superuser:
                        return Response(
                            {'error': 'Only kebele leaders can approve ID cards at kebele level'},
                            status=status.HTTP_403_FORBIDDEN
                        )
                    if id_card.status != IDCardStatus.PENDING_APPROVAL:
                        return Response(
                            {'error': 'Only pending ID cards can be approved by kebele leader'},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    # Check if current tenant is a kebele
                    if tenant.type != 'kebele':
                        return Response(
                            {'error': 'Kebele approval can only be done in kebele tenants'},
                            status=status.HTTP_403_FORBIDDEN
                        )

                    id_card.status = IDCardStatus.KEBELE_APPROVED
                    id_card.kebele_approved_by = request.user
                    id_card.kebele_approved_at = timezone.now()
                    id_card.kebele_approval_comment = comment
                    id_card.has_kebele_pattern = True  # Apply left half of castle pattern
                    id_card.submitted_to_subcity_at = timezone.now()  # Auto-submit to subcity
                    workflow_action = ApprovalAction.APPROVE

                elif action == 'subcity_approve':
                    # Debug user information
                    print(f"🔍 Subcity approval permission check:")
                    print(f"  User: {request.user}")
                    print(f"  User email: {getattr(request.user, 'email', 'No email')}")
                    print(f"  User role: {getattr(request.user, 'role', 'No role')}")
                    print(f"  User role type: {type(getattr(request.user, 'role', None))}")
                    print(f"  Is superuser: {getattr(request.user, 'is_superuser', False)}")

                    # Check if user is authenticated
                    if not request.user.is_authenticated:
                        print(f"❌ User not authenticated")
                        return Response(
                            {'error': 'Authentication required'},
                            status=status.HTTP_401_UNAUTHORIZED
                        )

                    # Get user role - handle both direct attribute and getattr safely
                    user_role = getattr(request.user, 'role', None)
                    is_superuser = getattr(request.user, 'is_superuser', False)

                    print(f"🔍 DETAILED ROLE TRACING:")
                    print(f"  User object: {request.user}")
                    print(f"  User type: {type(request.user)}")
                    print(f"  User ID: {request.user.id}")
                    print(f"  User email: {request.user.email}")
                    print(f"  User.__dict__: {request.user.__dict__}")
                    print(f"  request.user.role (direct): {request.user.role}")
                    print(f"  getattr(request.user, 'role'): {getattr(request.user, 'role', 'NOT_FOUND')}")
                    print(f"  hasattr(request.user, 'role'): {hasattr(request.user, 'role')}")
                    print(f"  Is superuser: {is_superuser}")

                    # Check if this is coming from JWT token or database
                    print(f"🔍 CHECKING JWT TOKEN:")
                    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
                    print(f"  Authorization header: {auth_header[:50]}...")

                    if auth_header.startswith('Bearer '):
                        token = auth_header.split(' ')[1]
                        try:
                            import jwt
                            # Decode without verification to see contents
                            decoded = jwt.decode(token, options={"verify_signature": False})
                            print(f"  JWT payload: {decoded}")
                            print(f"  JWT role: {decoded.get('role', 'NOT_FOUND')}")
                        except Exception as e:
                            print(f"  JWT decode error: {e}")

                    print(f"🔍 FINAL ROLE VALUES:")
                    print(f"  user_role variable: '{user_role}'")

                    # CRITICAL FIX: Extract role directly from JWT token
                    jwt_role = None
                    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
                    if auth_header.startswith('Bearer '):
                        token = auth_header.split(' ')[1]
                        try:
                            import jwt
                            # Decode without verification to get the role
                            decoded = jwt.decode(token, options={"verify_signature": False})
                            jwt_role = decoded.get('role')
                            print(f"🔍 Extracted JWT role: '{jwt_role}'")
                        except Exception as e:
                            print(f"❌ JWT decode error: {e}")

                    # Get database role for comparison
                    db_role = user_role  # This comes from database via request.user
                    print(f"🔍 Database role: '{db_role}'")
                    print(f"� JWT token role: '{jwt_role}'")

                    # Use JWT token role if available, otherwise fall back to database role
                    if jwt_role:
                        user_role = jwt_role
                        print(f"✅ Using JWT token role: '{user_role}'")

                        # If roles don't match, update database to keep them in sync
                        if jwt_role != db_role:
                            try:
                                fresh_user = User.objects.get(id=request.user.id)
                                fresh_user.role = jwt_role
                                fresh_user.save()
                                print(f"� Updated database role from '{db_role}' to '{jwt_role}'")
                            except Exception as e:
                                print(f"❌ Error updating database role: {e}")
                    else:
                        user_role = db_role
                        print(f"🔍 No JWT role found, using database role: '{user_role}'")

                    # TEMPORARY: Allow more roles for testing - subcity admins, city admins, and superusers can approve at subcity level
                    if user_role not in ['subcity_admin', 'city_admin', 'superadmin'] and not is_superuser:
                        print(f"❌ Permission denied - role '{user_role}' not in ['subcity_admin', 'city_admin', 'superadmin'] and not superuser")
                        return Response(
                            {'error': f'Only subcity admins can approve ID cards at subcity level. Your role: {user_role}'},
                            status=status.HTTP_403_FORBIDDEN
                        )

                    print(f"✅ Permission granted for role '{user_role}'")
                    if id_card.status != IDCardStatus.KEBELE_APPROVED:
                        return Response(
                            {'error': 'Only kebele-approved ID cards can be approved by subcity admin'},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    # Allow subcity approval from kebele tenant context (cross-tenant approval)
                    # Subcity admins can approve ID cards from their child kebeles
                    print(f"🔍 Subcity approval context check:")
                    print(f"  Current tenant: {tenant.name} (type: {tenant.type})")
                    print(f"  User role: {request.user.role}")
                    print(f"  User tenant: {getattr(request.user, 'tenant', None)}")

                    id_card.status = IDCardStatus.APPROVED
                    id_card.subcity_approved_by = request.user
                    id_card.subcity_approved_at = timezone.now()
                    id_card.subcity_approval_comment = comment
                    id_card.has_subcity_pattern = True  # Apply right half of castle pattern
                    workflow_action = ApprovalAction.APPROVE

                elif action == 'reject':
                    # Kebele leaders can reject pending cards, subcity admins can reject kebele-approved cards
                    if request.user.role not in ['kebele_leader', 'subcity_admin', 'superadmin'] and not request.user.is_superuser:
                        return Response(
                            {'error': 'Only kebele leaders and subcity admins can reject ID cards'},
                            status=status.HTTP_403_FORBIDDEN
                        )

                    # Determine which level is rejecting
                    if request.user.role == 'kebele_leader' and id_card.status == IDCardStatus.PENDING_APPROVAL:
                        id_card.status = IDCardStatus.REJECTED
                        id_card.kebele_approval_comment = comment
                    elif request.user.role == 'subcity_admin' and id_card.status == IDCardStatus.KEBELE_APPROVED:
                        id_card.status = IDCardStatus.REJECTED
                        id_card.subcity_approval_comment = comment
                        # Remove kebele pattern if subcity rejects
                        id_card.has_kebele_pattern = False
                    else:
                        return Response(
                            {'error': 'Invalid rejection - check ID card status and your role'},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    workflow_action = ApprovalAction.REJECT

                elif action == 'reset_to_draft':
                    # Allow clerks to reset rejected ID cards back to draft for resubmission
                    if request.user.role not in ['clerk', 'superadmin'] and not request.user.is_superuser:
                        return Response(
                            {'error': 'Only clerks and superadmins can reset ID cards to draft'},
                            status=status.HTTP_403_FORBIDDEN
                        )
                    if id_card.status != IDCardStatus.REJECTED:
                        return Response(
                            {'error': 'Only rejected ID cards can be reset to draft'},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    # Reset to draft and clear rejection data
                    id_card.status = IDCardStatus.DRAFT
                    id_card.kebele_approval_comment = None
                    id_card.subcity_approval_comment = None
                    id_card.has_kebele_pattern = False
                    id_card.has_subcity_pattern = False
                    id_card.submitted_for_approval_at = None
                    workflow_action = ApprovalAction.SUBMIT  # Use submit for workflow tracking

                else:
                    return Response(
                        {'error': f'Invalid action: {action}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Save the ID card
                id_card.save()

                # Create workflow log
                print(f"🔍 DEBUG: About to create WorkflowLog with workflow_action: {workflow_action}")
                print(f"🔍 DEBUG: workflow_action type: {type(workflow_action)}")
                WorkflowLog.objects.create(
                    id_card=id_card,
                    action=workflow_action,
                    from_status=old_status,
                    to_status=id_card.status,
                    comment=comment,
                    performed_by=request.user
                )

                # Return updated ID card
                return Response(self.get_serializer(id_card).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated, CanPrintIDCards])
    def print(self, request, pk=None, tenant_id=None):
        """
        Print an ID card and mark it as printed.
        CRITICAL SECURITY: Only subcity admins and higher can print ID cards.
        """
        # Permission check is handled by CanPrintIDCards permission class

        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            id_card = self.get_object()

            # Check if ID card is approved and ready for printing
            if id_card.status != IDCardStatus.APPROVED:
                return Response(
                    {'error': 'Only fully approved ID cards can be printed'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Mark as printed
            id_card.status = IDCardStatus.PRINTED
            id_card.printed_at = timezone.now()
            id_card.printed_by = request.user
            id_card.save()

            # Log the workflow action
            WorkflowLog.objects.create(
                id_card=id_card,
                action=ApprovalAction.PRINT,
                from_status=IDCardStatus.APPROVED,
                to_status=IDCardStatus.PRINTED,
                performed_by=request.user,
                comment='ID card printed'
            )

            print(f"✅ ID card {id_card.card_number} marked as printed by {request.user.email}")

            return Response(IDCardSerializer(id_card).data)

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def auth_test(self, request, tenant_id=None):
        """
        Simple endpoint to test authentication status.
        """
        return Response({
            'authenticated': request.user.is_authenticated,
            'user_id': request.user.id if request.user.is_authenticated else None,
            'email': getattr(request.user, 'email', None),
            'role': getattr(request.user, 'role', None),
            'is_superuser': getattr(request.user, 'is_superuser', False),
            'tenant_id': tenant_id
        })

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def tenant_info(self, request, tenant_id=None):
        """Get tenant information and hierarchy."""
        tenant = self.get_tenant()

        # Build tenant hierarchy
        hierarchy = {
            'current': {
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type,
                'schema_name': tenant.schema_name
            }
        }

        # Add parent information if exists
        if tenant.parent:
            hierarchy['parent'] = {
                'id': tenant.parent.id,
                'name': tenant.parent.name,
                'type': tenant.parent.type,
                'schema_name': tenant.parent.schema_name
            }

            # Add grandparent (city) if exists
            if tenant.parent.parent:
                hierarchy['grandparent'] = {
                    'id': tenant.parent.parent.id,
                    'name': tenant.parent.parent.name,
                    'type': tenant.parent.parent.type,
                    'schema_name': tenant.parent.parent.schema_name
                }

        # Add children information
        children = tenant.children.all()
        if children.exists():
            hierarchy['children'] = [
                {
                    'id': child.id,
                    'name': child.name,
                    'type': child.type,
                    'schema_name': child.schema_name
                }
                for child in children
            ]

        return Response({
            'tenant': hierarchy,
            'timestamp': timezone.now().isoformat()
        })

    @action(detail=True, methods=['get'])
    def cross_tenant_detail(self, request, tenant_id=None, pk=None):
        """
        Get ID card details from any child tenant for subcity admins.
        This allows subcity admins to view ID card details from their kebele tenants.
        """
        # Check if user is subcity admin or higher
        if request.user.role not in ['subcity_admin', 'city_admin', 'superadmin'] and not request.user.is_superuser:
            return Response(
                {'error': 'Only subcity admins and above can view cross-tenant ID card details'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()

        # Check if current tenant is a subcity or city
        if tenant.type not in ['subcity', 'city']:
            return Response(
                {'error': 'This endpoint is only available for subcity and city tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all child kebele tenants
        if tenant.type == 'subcity':
            child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')
        else:  # city
            # For city admins, get all kebeles under all subcities
            child_subcities = Tenant.objects.filter(parent=tenant, type='subcity')
            child_kebeles = Tenant.objects.filter(parent__in=child_subcities, type='kebele')

        # Search for the ID card in all child kebele schemas
        for kebele in child_kebeles:
            try:
                with schema_context(kebele.schema_name):
                    id_card = IDCard.objects.select_related('citizen').get(pk=pk)

                    # Serialize the ID card with tenant info
                    serializer = self.get_serializer(id_card)
                    card_data = serializer.data

                    # Add kebele tenant info
                    card_data['kebele_tenant'] = {
                        'id': kebele.id,
                        'name': kebele.name,
                        'schema_name': kebele.schema_name,
                        'parent_id': kebele.parent.id if kebele.parent else None,
                        'parent_name': kebele.parent.name if kebele.parent else None
                    }

                    # Add citizen details if available
                    if id_card.citizen:
                        from tenants.serializers import TenantCitizenSerializer
                        citizen_serializer = TenantCitizenSerializer(id_card.citizen)
                        card_data['citizen_details'] = citizen_serializer.data

                    return Response(card_data)

            except IDCard.DoesNotExist:
                continue
            except Exception as e:
                print(f"❌ Error searching in kebele {kebele.name}: {str(e)}")
                continue

        # If not found in any kebele
        return Response(
            {'error': f'ID card {pk} not found in any child kebele tenants'},
            status=status.HTTP_404_NOT_FOUND
        )

    @action(detail=False, methods=['get'])
    def pending_subcity_approval(self, request, tenant_id=None):
        """
        Get ID cards that are pending subcity approval.
        Only accessible by subcity admins to view kebele-approved cards from child kebeles.
        """
        # Check if user is subcity admin
        if request.user.role not in ['subcity_admin', 'superadmin'] and not request.user.is_superuser:
            return Response(
                {'error': 'Only subcity admins can view pending subcity approvals'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()

        # Check if current tenant is a subcity
        if tenant.type != 'subcity':
            return Response(
                {'error': 'This endpoint is only available for subcity tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all child kebele tenants
        child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')

        all_pending_cards = []

        # Collect kebele-approved cards from all child kebeles
        for kebele in child_kebeles:
            with schema_context(kebele.schema_name):
                from citizens.models import Citizen

                # Only show ID cards for active citizens (filter out transferred citizens)
                active_citizen_ids = Citizen.objects.filter(is_active=True).values_list('id', flat=True)
                kebele_approved_cards = IDCard.objects.filter(
                    status=IDCardStatus.KEBELE_APPROVED,
                    citizen_id__in=active_citizen_ids
                ).select_related('citizen', 'kebele_approved_by')

                # Add tenant info to each card
                for card in kebele_approved_cards:
                    card_data = self.get_serializer(card).data
                    card_data['kebele_tenant'] = {
                        'id': kebele.id,
                        'name': kebele.name,
                        'schema_name': kebele.schema_name
                    }
                    all_pending_cards.append(card_data)

        return Response({
            'count': len(all_pending_cards),
            'results': all_pending_cards
        })

    @action(detail=False, methods=['get'])
    def printing_queue(self, request, tenant_id=None):
        """
        Get ID cards that are ready for printing (fully approved).
        Only accessible by subcity admins to view approved cards from child kebeles.
        """
        # Check if user is subcity admin or higher
        if request.user.role not in ['subcity_admin', 'city_admin', 'superadmin'] and not request.user.is_superuser:
            return Response(
                {'error': 'Only subcity admins and above can view the printing queue'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()

        # Check if current tenant is a subcity or city
        if tenant.type not in ['subcity', 'city']:
            return Response(
                {'error': 'This endpoint is only available for subcity and city tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all child kebele tenants
        if tenant.type == 'subcity':
            child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')
        else:  # city
            # For city admins, get all kebeles under all subcities
            child_subcities = Tenant.objects.filter(parent=tenant, type='subcity')
            child_kebeles = Tenant.objects.filter(parent__in=child_subcities, type='kebele')

        all_approved_cards = []

        # Collect fully approved cards from all child kebeles
        for kebele in child_kebeles:
            with schema_context(kebele.schema_name):
                from citizens.models import Citizen

                # Only show ID cards for active citizens (filter out transferred citizens)
                active_citizen_ids = Citizen.objects.filter(is_active=True).values_list('id', flat=True)
                approved_cards = IDCard.objects.filter(
                    status=IDCardStatus.APPROVED,  # Only fully approved cards ready for printing
                    citizen_id__in=active_citizen_ids
                ).select_related('citizen', 'subcity_approved_by')

                # Add tenant info to each card
                for card in approved_cards:
                    card_data = self.get_serializer(card).data
                    card_data['kebele_tenant'] = {
                        'id': kebele.id,
                        'name': kebele.name,
                        'schema_name': kebele.schema_name
                    }
                    all_approved_cards.append(card_data)

        # Sort by subcity approval date (most recent first)
        all_approved_cards.sort(
            key=lambda x: x.get('subcity_approved_at') or x.get('approved_at') or '',
            reverse=True
        )

        return Response({
            'count': len(all_approved_cards),
            'results': all_approved_cards
        })

    @action(detail=False, methods=['get'])
    def cross_tenant_list(self, request, tenant_id=None):
        """
        Get ID cards from all child tenants for subcity admins.
        This allows subcity admins to see ID cards from all their kebele tenants.
        """
        # Check if user has subcity admin permissions
        # Allow users with ID card approval permissions or higher
        if not (request.user.has_group_permission('approve_id_cards') or
                request.user.has_group_permission('create_kebele_users') or
                request.user.has_group_permission('manage_users') or
                request.user.has_group_permission('view_tenant') or
                request.user.has_group_permission('view_child_subcities_data') or
                request.user.role in ['subcity_admin', 'city_admin', 'superadmin'] or
                request.user.is_superuser):
            return Response(
                {'error': 'Only users with administrative permissions can view cross-tenant ID cards'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()

        # Check if current tenant is a subcity
        if tenant.type != 'subcity':
            return Response(
                {'error': 'This endpoint is only available for subcity tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get pagination parameters
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        search = request.GET.get('search', '')
        status_filter = request.GET.get('status', '')

        # Get all child kebele tenants
        child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')

        all_cards = []

        # Collect ID cards from all child kebeles
        for kebele in child_kebeles:
            with schema_context(kebele.schema_name):
                from citizens.models import Citizen

                # By default, only show ID cards for active citizens (hide transferred citizens)
                show_transferred = request.GET.get('show_transferred', 'false').lower() == 'true'
                if show_transferred:
                    # Show all ID cards including those for transferred citizens
                    queryset = IDCard.objects.select_related('citizen').filter(
                        status__in=[IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                    )
                else:
                    # Only show ID cards for active citizens (filter out transferred citizens)
                    active_citizen_ids = Citizen.objects.filter(is_active=True).values_list('id', flat=True)
                    queryset = IDCard.objects.select_related('citizen').filter(
                        status__in=[IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED],
                        citizen_id__in=active_citizen_ids
                    )

                # Apply search filter
                if search:
                    queryset = queryset.filter(
                        Q(citizen__first_name__icontains=search) |
                        Q(citizen__middle_name__icontains=search) |
                        Q(citizen__last_name__icontains=search) |
                        Q(citizen__digital_id__icontains=search) |
                        Q(card_number__icontains=search)
                    )

                # Apply status filter (but still within kebele-approved and above)
                if status_filter:
                    allowed_statuses = [IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                    if status_filter in allowed_statuses:
                        queryset = queryset.filter(status=status_filter)
                    else:
                        # If filtering for draft/pending/rejected, show nothing since those aren't kebele-approved
                        queryset = queryset.none()

                # Add tenant info to each card
                for card in queryset:
                    card_data = self.get_serializer(card).data
                    card_data['kebele_tenant'] = {
                        'id': kebele.id,
                        'name': kebele.name,
                        'schema_name': kebele.schema_name
                    }
                    all_cards.append(card_data)

        # Sort by created_at descending
        all_cards.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Apply pagination
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_cards = all_cards[start_index:end_index]

        return Response({
            'count': len(all_cards),
            'results': paginated_cards,
            'page': page,
            'page_size': page_size,
            'total_pages': (len(all_cards) + page_size - 1) // page_size
        })

    @action(detail=False, methods=['get'])
    def stats(self, request, *args, **kwargs):
        """Get statistics for ID cards in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            total_id_cards = IDCard.objects.count()

            # Status distribution
            status_counts = {}
            for status_choice in IDCardStatus.choices:
                status_code = status_choice[0]
                status_counts[status_code] = IDCard.objects.filter(status=status_code).count()

            # Monthly statistics
            monthly_stats = (
                IDCard.objects.annotate(month=TruncMonth('created_at'))
                .values('month')
                .annotate(count=Count('id'))
                .order_by('month')
            )

            monthly_data = []
            for item in monthly_stats:
                if item['month']:  # Check if month is not None
                    monthly_data.append({
                        'month': item['month'].strftime('%B %Y'),
                        'count': item['count']
                    })

            stats = {
                'total_id_cards': total_id_cards,
                'status_distribution': status_counts,
                'monthly_statistics': monthly_data
            }

            return Response(stats)


class TenantSpecificIDCardTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing ID card templates within a specific tenant schema.

    Templates are shared across tenants but accessed within tenant context.
    """
    serializer_class = IDCardTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]

    def get_tenant(self):
        """Get the tenant from URL parameter, bypassing any middleware issues."""
        tenant_id = self.kwargs.get('tenant_id')
        try:
            # Direct database lookup to bypass any middleware issues
            tenant = Tenant.objects.get(id=tenant_id)
            return tenant
        except Tenant.DoesNotExist:
            from django.http import Http404
            raise Http404(f"Tenant {tenant_id} not found")

    def get_queryset(self):
        """Get active ID card templates within the tenant's schema context."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            return IDCardTemplate.objects.filter(is_active=True)

    def list(self, request, *args, **kwargs):
        """List ID card templates within the tenant's schema context."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def default(self, request, tenant_id=None):
        """Get the default template for the current tenant."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            try:
                default_template = IDCardTemplate.objects.get(is_default=True, is_active=True)
                serializer = self.get_serializer(default_template)
                return Response(serializer.data)
            except IDCardTemplate.DoesNotExist:
                # Return the first active template if no default is set
                try:
                    first_template = IDCardTemplate.objects.filter(is_active=True).first()
                    if first_template:
                        serializer = self.get_serializer(first_template)
                        return Response(serializer.data)
                    else:
                        return Response(
                            {'detail': 'No templates available'},
                            status=status.HTTP_404_NOT_FOUND
                        )
                except:
                    return Response(
                        {'detail': 'No templates available'},
                        status=status.HTTP_404_NOT_FOUND
                    )
