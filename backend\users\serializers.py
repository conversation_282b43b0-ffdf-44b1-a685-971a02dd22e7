from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from .models import User
from tenants.models.tenant import Tenant


class UserSerializer(serializers.ModelSerializer):
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    primary_group_id = serializers.SerializerMethodField()
    primary_group_name = serializers.SerializerMethodField()
    groups = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('id', 'email', 'username', 'first_name', 'last_name', 'role',
                  'tenant', 'tenant_name', 'phone_number', 'profile_picture',
                  'is_active', 'date_joined', 'primary_group_id', 'primary_group_name', 'groups', 'permissions')
        read_only_fields = ('date_joined',)

    def get_primary_group_id(self, obj):
        """Get the user's primary group ID"""
        try:
            primary_group = obj.get_primary_group()
            return primary_group.id if primary_group else None
        except:
            return None

    def get_primary_group_name(self, obj):
        """Get the user's primary group name"""
        try:
            primary_group = obj.get_primary_group()
            return primary_group.name if primary_group else None
        except:
            return None

    def get_groups(self, obj):
        """Get all groups for this user"""
        try:
            groups = obj.get_user_groups()
            return [
                {
                    'id': group.id,
                    'name': group.name,
                    'description': group.description,
                    'group_type': group.group_type,
                    'level': group.level,
                    'is_system_group': group.is_system_group,
                    'is_primary': group.id == self.get_primary_group_id(obj)
                }
                for group in groups
            ]
        except Exception as e:
            # Log the error for debugging
            print(f"Error getting groups for user {obj.email}: {e}")
            return []

    def get_permissions(self, obj):
        """Get all permissions for this user through their groups"""
        try:
            permissions = obj.get_effective_permissions()
            return [
                {
                    'codename': perm.codename,
                    'name': perm.name,
                    'content_type': f"{perm.content_type.app_label}.{perm.content_type.model}"
                }
                for perm in permissions
            ]
        except Exception as e:
            # Log the error for debugging
            print(f"Error getting permissions for user {obj.email}: {e}")
            return []


class UserCreateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True, required=True)
    primary_group_id = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        model = User
        fields = ('id', 'email', 'username', 'password', 'password2', 'first_name',
                  'last_name', 'role', 'tenant', 'phone_number', 'profile_picture', 'primary_group_id')

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        primary_group_id = validated_data.pop('primary_group_id', None)
        validated_data.pop('password2')
        user = User.objects.create_user(**validated_data)

        # Assign user to primary group if specified
        if primary_group_id:
            try:
                from .models_groups import TenantGroup
                from django_tenants.utils import schema_context, get_public_schema_name

                # Always query groups from public schema
                with schema_context(get_public_schema_name()):
                    group = TenantGroup.objects.get(id=primary_group_id, is_active=True)

                    # Ensure user has tenant information for group membership
                    if not user.tenant:
                        # Get tenant from request context
                        request = self.context.get('request')
                        if request and hasattr(request, 'tenant'):
                            user.tenant = request.tenant
                            user.save()

                    user.add_to_group(group, is_primary=True, reason='Assigned during user creation')
                    print(f"✅ User {user.email} assigned to group {group.name} (ID: {group.id}) as primary")
            except TenantGroup.DoesNotExist:
                # Don't fail user creation if group doesn't exist, just log it
                print(f"Warning: Primary group {primary_group_id} not found for user {user.email}")
            except Exception as e:
                print(f"Error assigning user {user.email} to group {primary_group_id}: {e}")

        return user


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password2 = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password2']:
            raise serializers.ValidationError({"new_password": "Password fields didn't match."})
        return attrs


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    username_field = 'email'  # Default to email field

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add username field to support domain-based login
        self.fields['username'] = serializers.CharField(required=False)
        # Make email field optional since we support username-based login
        self.fields['email'].required = False

    @classmethod
    def get_token(cls, user):
        """Override to add custom claims to the token"""
        token = super().get_token(user)

        # Get tenant information
        tenant = getattr(user, '_tenant', None) or user.tenant
        domain = getattr(user, '_domain', None)

        # Determine the appropriate role based on tenant context
        # First check if user has a tenant-specific role from authentication
        user_role = getattr(user, '_tenant_role', None)
        if not user_role:
            user_role = cls._determine_role_for_tenant(user, tenant)

        # Debug logging
        print(f"🔍 JWT Token Generation:")
        print(f"  User: {user.username} ({user.email})")
        print(f"  Database role: {user.role}")
        print(f"  Tenant-specific role: {getattr(user, '_tenant_role', 'None')}")
        print(f"  Final JWT role: {user_role}")
        print(f"  Tenant: {tenant.name if tenant else 'None'} (Type: {tenant.type if tenant else 'None'})")
        print(f"  Is Superuser: {user.is_superuser}")

        # Add user information to token
        token['email'] = user.email
        token['username'] = user.username
        token['role'] = user_role  # Use tenant-specific role
        token['is_superuser'] = user.is_superuser

        # Add user permissions to token
        try:
            # Get user permissions using the same method as UserSerializer
            permissions = user.get_effective_permissions()
            # Only include permission codenames to keep JWT token size manageable
            permission_codes = [perm.codename for perm in permissions]
            token['permissions'] = permission_codes
            print(f"  Permissions: {len(permission_codes)} permissions")
        except Exception as e:
            print(f"  Permissions error: {e}")
            token['permissions'] = []

        # Add user groups to token
        try:
            # Get user's group memberships
            groups = user.get_user_groups()
            # Only include group names to keep JWT token size manageable
            group_names = [group.name for group in groups]
            token['groups'] = group_names
            print(f"  Groups: {group_names}")
        except Exception as e:
            print(f"  Groups error: {e}")
            token['groups'] = []

        # Add tenant information if available
        if tenant:
            token['tenant_id'] = tenant.id
            token['tenant_name'] = tenant.name
            token['tenant_name_am'] = tenant.name_am  # Add Amharic name from database
            token['tenant_type'] = tenant.type
            token['tenant_schema'] = tenant.schema_name

            # Add parent tenant info if available (with proper database fetch)
            if tenant.parent:
                # Fetch parent tenant with Amharic name to ensure it's loaded
                parent_tenant = Tenant.objects.select_related().get(id=tenant.parent.id)
                token['parent_tenant_id'] = parent_tenant.id
                token['parent_tenant_name'] = parent_tenant.name
                token['parent_tenant_name_am'] = parent_tenant.name_am  # Add Amharic name from database
                token['parent_tenant_type'] = parent_tenant.type

            # Add city information for all tenant types (with proper database fetch)
            city_tenant = None
            if tenant.type == 'city':
                city_tenant = tenant
            elif tenant.type == 'subcity' and tenant.parent:
                city_tenant = Tenant.objects.get(id=tenant.parent.id)
            elif tenant.type == 'kebele' and tenant.parent and tenant.parent.parent:
                city_tenant = Tenant.objects.get(id=tenant.parent.parent.id)

            if city_tenant:
                token['city_tenant_id'] = city_tenant.id
                token['city_tenant_name'] = city_tenant.name
                token['city_tenant_name_am'] = city_tenant.name_am  # Add Amharic name from database
                token['city_tenant_type'] = city_tenant.type

            # Add domain info if available
            if domain:
                token['domain'] = domain.domain

        return token

    @classmethod
    def _determine_role_for_tenant(cls, user, tenant):
        """
        Determine the appropriate role for a user in a specific tenant context.
        This prevents role persistence across different tenant logins.
        """
        # Superusers and superadmins keep their role regardless of tenant
        if user.is_superuser or user.role == 'superadmin':
            return user.role

        # If no tenant context, use the user's default role
        if not tenant:
            return user.role

        # If user's tenant matches the login tenant, use their role
        if user.tenant and user.tenant.id == tenant.id:
            return user.role

        # For cross-tenant access, determine role based on hierarchy and permissions
        if user.tenant:
            # City admin accessing subcity or kebele under their city
            if (user.role == 'city_admin' and user.tenant.type == 'city'):
                if tenant.type == 'subcity' and tenant.parent and tenant.parent.id == user.tenant.id:
                    return 'city_admin'  # Maintain city_admin role in subcity
                elif tenant.type == 'kebele' and tenant.parent and tenant.parent.parent and tenant.parent.parent.id == user.tenant.id:
                    return 'city_admin'  # Maintain city_admin role in kebele

            # Subcity admin accessing kebele under their subcity
            elif (user.role == 'subcity_admin' and user.tenant.type == 'subcity'):
                if tenant.type == 'kebele' and tenant.parent and tenant.parent.id == user.tenant.id:
                    return 'subcity_admin'  # Maintain subcity_admin role in kebele

        # For users accessing tenants they don't belong to or have hierarchy access to,
        # determine role based on what's appropriate for the target tenant
        if tenant.type == 'city':
            # Only city_admin role is valid for city tenants
            return 'city_admin' if user.role in ['city_admin', 'superadmin'] else 'clerk'
        elif tenant.type == 'subcity':
            # Only subcity_admin role is valid for subcity tenants
            return 'subcity_admin' if user.role in ['subcity_admin', 'city_admin', 'superadmin'] else 'clerk'
        elif tenant.type == 'kebele':
            # For kebele tenants, default to clerk unless user has specific kebele roles
            if user.role in ['kebele_leader', 'kebele_admin']:
                return user.role
            else:
                return 'clerk'  # Default to clerk for kebele access

        # Fallback to user's original role
        return user.role

    def validate(self, attrs):
        # Support both email and username fields
        email = attrs.get('email')
        username = attrs.get('username')
        password = attrs.get('password')

        # Determine which field to use for authentication
        if username and not email:
            # Domain-based login: use username field
            attrs[self.username_field] = username
        elif email and not username:
            # Regular email login: use email field
            attrs[self.username_field] = email
        elif email:
            # Both provided: prefer email for superadmin
            attrs[self.username_field] = email
        else:
            raise serializers.ValidationError('Either email or username is required')

        # Custom authentication for domain-based login
        if username and not email:
            # For domain-based login, use Django's authenticate directly
            from django.contrib.auth import authenticate

            user = authenticate(
                request=self.context.get('request'),
                username=username,
                password=password
            )

            if user is None:
                raise serializers.ValidationError('No active account found with the given credentials')

            # Set the user for token generation
            self.user = user

            # Generate token data manually using our custom get_token method
            refresh = self.get_token(user)

            data = {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }

        else:
            # For regular email-based login, use the parent validation
            data = super().validate(attrs)

            # Add custom claims to regular tokens too using our custom get_token method
            refresh = self.get_token(self.user)

            # Update the data with new tokens that include custom claims
            data.update({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            })

        # Check if user was authenticated with domain-based backend
        tenant = getattr(self.user, '_tenant', None) or self.user.tenant
        domain = getattr(self.user, '_domain', None)

        # Add custom claims
        tenant_data = None
        if tenant:
            tenant_data = {
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type,
                'schema_name': tenant.schema_name,
            }

            # Add parent tenant info if available
            if tenant.parent:
                tenant_data['parent'] = {
                    'id': tenant.parent.id,
                    'name': tenant.parent.name,
                    'type': tenant.parent.type,
                }

            # Add domain info if available
            if domain:
                tenant_data['domain'] = domain.domain

        data.update({
            'id': self.user.id,
            'email': self.user.email,
            'username': self.user.username,
            'first_name': self.user.first_name,
            'last_name': self.user.last_name,
            'role': self.user.role,
            'tenant': tenant_data,
            'is_superuser': self.user.is_superuser,
        })

        return data
