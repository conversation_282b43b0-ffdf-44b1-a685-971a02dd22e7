import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Avatar,
  CircularProgress,
  Alert,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Pagination,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Search as SearchIcon,
  LocationCity as CityIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Print as PrintIcon,
  Person as PersonIcon,
  FilterList as FilterIcon,
  Business as BusinessIcon,
  LocationOn as LocationOnIcon,
  Phone as PhoneIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';

const CitizenDirectory = () => {
  const { user } = useAuth();
  const { t } = useLocalization();
  const navigate = useNavigate();
  const [citizenData, setCitizenData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    subcity: '',
    kebele: '',
    gender: '',
    age_group: ''
  });

  useEffect(() => {
    if (user) {
      fetchCitizenData();
    }
  }, [user, page, filters]);

  const fetchCitizenData = async () => {
    try {
      setLoading(true);

      // Get tenant ID
      let tenantId = user?.tenant_id;
      if (!tenantId) {
        const token = localStorage.getItem('accessToken');
        if (token) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          tenantId = payload.tenant_id;
        }
      }

      if (!tenantId) {
        throw new Error('No tenant ID available');
      }

      const params = new URLSearchParams({
        page: page.toString(),
        page_size: '40',
        ...filters
      });

      console.log('🔍 Calling API with tenant ID:', tenantId);
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/citizen-book/data/?${params}`);
      console.log('🔍 API Response:', response.data);
      setCitizenData(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching citizen data:', error);
      setError(`Failed to load citizen data: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateBook = () => {
    navigate('/citizens/citizen-book/print');
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(1); // Reset to first page when filtering
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const getAgeGroupColor = (ageGroup) => {
    switch (ageGroup) {
      case '18-30':
        return '#10B981';
      case '31-50':
        return '#3B82F6';
      case '51+':
        return '#8B5CF6';
      default:
        return '#6B7280';
    }
  };

  if (!user || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <CircularProgress size={60} />
          <Typography variant="h6" className="mt-4">
            Loading Citizens...
          </Typography>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert severity="error" className="m-4">
        {error}
      </Alert>
    );
  }

  if (!citizenData) {
    return (
      <Alert severity="info" className="m-4">
        {t('no_citizen_data_available', 'No citizen data available')}
      </Alert>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <CityIcon className="text-4xl mr-3" style={{ color: '#ff8f00' }} />
          <div>
            <Typography variant="h4" className="font-bold" style={{ color: '#ff8f00' }}>
              {citizenData.city_info?.name} Citizen Book
            </Typography>
            <Typography variant="subtitle1" className="text-gray-600">
              {t('complete_citizen_directory', 'Complete citizen directory for the city')}
            </Typography>
          </div>
        </div>
        <Button
          variant="contained"
          startIcon={<PrintIcon />}
          onClick={handleGenerateBook}
          className="bg-orange-600 hover:bg-orange-700"
          style={{ backgroundColor: '#ff8f00', color: 'white' }}
        >
          {t('generate_printable_book', 'Generate Printable Book')}
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="text-center">
            <Typography variant="h4" className="font-bold">
              {citizenData.statistics?.total_citizens?.toLocaleString() || 0}
            </Typography>
            <Typography variant="body2">Total Citizens</Typography>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="text-center">
            <Typography variant="h4" className="font-bold">
              {citizenData.statistics?.total_subcities || 0}
            </Typography>
            <Typography variant="body2">Sub-Cities</Typography>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="text-center">
            <Typography variant="h4" className="font-bold">
              {citizenData.statistics?.total_kebeles || 0}
            </Typography>
            <Typography variant="body2">Kebeles</Typography>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="text-center">
            <Typography variant="h6" className="font-bold">
              {citizenData.statistics?.total_male || 0}M / {citizenData.statistics?.total_female || 0}F
            </Typography>
            <Typography variant="body2">Gender Ratio</Typography>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent>
          <div className="flex items-center mb-4">
            <FilterIcon className="mr-2" style={{ color: '#ff8f00' }} />
            <Typography variant="h6" style={{ color: '#ff8f00' }}>
              Filters
            </Typography>
          </div>

          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label={t('search_citizens', 'Search Citizens')}
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                placeholder={t('name_id_phone_email', 'Name, ID, Phone, Email...')}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>{t('sub_city', 'Sub-City')}</InputLabel>
                <Select
                  value={filters.subcity}
                  onChange={(e) => handleFilterChange('subcity', e.target.value)}
                  label={t('sub_city', 'Sub-City')}
                >
                  <MenuItem value="">{t('all_sub_cities', 'All Sub-Cities')}</MenuItem>
                  {citizenData.subcity_summary?.map((subcity) => (
                    <MenuItem key={subcity.id} value={subcity.id}>
                      {subcity.name} ({subcity.citizens_count})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>{t('gender', 'Gender')}</InputLabel>
                <Select
                  value={filters.gender}
                  onChange={(e) => handleFilterChange('gender', e.target.value)}
                  label={t('gender', 'Gender')}
                >
                  <MenuItem value="">{t('all_genders', 'All Genders')}</MenuItem>
                  <MenuItem value="male">{t('male', 'Male')}</MenuItem>
                  <MenuItem value="female">{t('female', 'Female')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>{t('age_group', 'Age Group')}</InputLabel>
                <Select
                  value={filters.age_group}
                  onChange={(e) => handleFilterChange('age_group', e.target.value)}
                  label={t('age_group', 'Age Group')}
                >
                  <MenuItem value="">{t('all_ages', 'All Ages')}</MenuItem>
                  <MenuItem value="18-30">{t('age_18_30', '18-30 years')}</MenuItem>
                  <MenuItem value="31-50">{t('age_31_50', '31-50 years')}</MenuItem>
                  <MenuItem value="51+">{t('age_51_plus', '51+ years')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                onClick={() => {
                  setFilters({
                    search: '',
                    subcity: '',
                    kebele: '',
                    gender: '',
                    age_group: ''
                  });
                  setPage(1);
                }}
                className="h-full"
              >
                {t('clear_filters', 'Clear Filters')}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Citizens Table */}
      <Card>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6" style={{ color: '#ff8f00' }}>
              Citizens Directory ({(citizenData.count || citizenData.citizens?.length || 0).toLocaleString()} total)
            </Typography>
            <Typography variant="body2" className="text-gray-600">
              Page {page} of {citizenData.total_pages || 1}
            </Typography>
          </div>

          {(citizenData.results || citizenData.citizens)?.length === 0 ? (
            <div className="text-center py-8">
              <PersonIcon className="text-6xl text-gray-400 mb-4" />
              <Typography variant="h6" className="text-gray-500">
                {t('no_citizens_found', 'No citizens found')}
              </Typography>
              <Typography variant="body2" className="text-gray-400">
                {t('try_adjusting_filters', 'Try adjusting your filters or search criteria')}
              </Typography>
            </div>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table size="small" sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>{t('photo', 'Photo')}</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>{t('name', 'Name')}</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>{t('id_number', 'ID Number')}</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>{t('date_of_birth', 'Date of Birth')}</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>{t('gender', 'Gender')}</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>{t('phone', 'Phone')}</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>{t('location', 'Location')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(citizenData.results || citizenData.citizens)?.map((citizen, index) => {
                    // Determine photo source
                    const getPhotoSrc = () => {
                      if (citizen.photo_base64) {
                        // Handle base64 photo
                        if (citizen.photo_base64.startsWith('data:')) {
                          return citizen.photo_base64;
                        } else {
                          return `data:image/jpeg;base64,${citizen.photo_base64}`;
                        }
                      } else if (citizen.photo_url) {
                        // Handle photo URL
                        return citizen.photo_url;
                      }
                      return null;
                    };

                    return (
                      <TableRow
                        key={`${citizen.kebele?.id || 'unknown'}-${citizen.id}`}
                        hover
                        sx={{
                          '&:nth-of-type(odd)': { backgroundColor: '#fafafa' },
                          height: '60px'
                        }}
                      >
                        {/* Photo */}
                        <TableCell sx={{ padding: '8px' }}>
                          <Avatar
                            src={getPhotoSrc()}
                            sx={{
                              width: 40,
                              height: 40,
                              backgroundColor: citizen.gender === 'male' ? '#3B82F6' : '#EC4899',
                              color: 'white'
                            }}
                          >
                            {!getPhotoSrc() && (citizen.gender === 'male' ? <MaleIcon fontSize="small" /> : <FemaleIcon fontSize="small" />)}
                          </Avatar>
                        </TableCell>

                        {/* Name */}
                        <TableCell sx={{ padding: '8px' }}>
                          <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.875rem' }}>
                            {citizen.full_name}
                          </Typography>
                          {citizen.age_group && (
                            <Chip
                              size="small"
                              label={citizen.age_group}
                              sx={{
                                backgroundColor: getAgeGroupColor(citizen.age_group),
                                color: 'white',
                                fontSize: '0.7rem',
                                height: '20px',
                                mt: 0.5
                              }}
                            />
                          )}
                        </TableCell>

                        {/* ID Number */}
                        <TableCell sx={{ padding: '8px' }}>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontFamily: 'monospace' }}>
                            {citizen.digital_id || 'N/A'}
                          </Typography>
                        </TableCell>

                        {/* Date of Birth */}
                        <TableCell sx={{ padding: '8px' }}>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {citizen.date_of_birth ? new Date(citizen.date_of_birth).toLocaleDateString() : 'N/A'}
                          </Typography>
                        </TableCell>

                        {/* Gender */}
                        <TableCell sx={{ padding: '8px' }}>
                          <Chip
                            icon={citizen.gender === 'male' ? <MaleIcon fontSize="small" /> : <FemaleIcon fontSize="small" />}
                            label={citizen.gender === 'male' ? t('male', 'Male') : t('female', 'Female')}
                            size="small"
                            color={citizen.gender === 'male' ? 'primary' : 'secondary'}
                            variant="outlined"
                            sx={{ fontSize: '0.75rem' }}
                          />
                        </TableCell>

                        {/* Phone */}
                        <TableCell sx={{ padding: '8px' }}>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {citizen.phone || 'N/A'}
                          </Typography>
                        </TableCell>

                        {/* Location */}
                        <TableCell sx={{ padding: '8px' }}>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 'medium' }}>
                            {citizen.kebele?.name || citizen.kebele_name}
                          </Typography>
                          <Typography variant="caption" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                            {citizen.subcity?.name || citizen.subcity_name}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Pagination */}
          {(citizenData.total_pages > 1) && (
            <div className="flex justify-center mt-6">
              <Pagination
                count={citizenData.total_pages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                size="large"
                showFirstButton
                showLastButton
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CitizenDirectory;
