/**
 * Localization Service for GoID System
 * Handles language switching and translation management
 */

import axios from '../utils/axios';

class LocalizationService {
    constructor() {
        this.currentLanguage = localStorage.getItem('goid_language') || 'en';
        this.translations = {};
        this.availableLanguages = [
            { code: 'en', name: 'English', nativeName: 'English' },
            { code: 'am', name: 'Amharic', nativeName: 'አማርኛ' },
            { code: 'om', name: '<PERSON><PERSON>', nativeName: '<PERSON>fa<PERSON>' },
            { code: 'ti', name: 'Tigrinya', nativeName: 'ትግርኛ' },
            { code: 'so', name: 'Somali', nativeName: '<PERSON><PERSON><PERSON>' },
            { code: 'aa', name: 'Afar', nativeName: 'Qafar' }
        ];

        // Initialize with default translations
        this.initializeDefaultTranslations();
    }

    /**
     * Initialize default translations for offline use
     */
    initializeDefaultTranslations() {
        this.translations = {
            en: {
                // Navigation
                dashboard: 'Dashboard',
                dashboard_welcome: 'Welcome to your ID management dashboard',
                citizens: 'Citizens',
                id_cards: 'ID Cards',
                clearance: 'Clearance',
                transfer: 'Transfer',
                print_queue: 'Print Queue',
                service_requests: 'Service Requests',
                kebele_users: 'Kebele Users',
                reports: 'Reports',
                citizen_directory: 'Citizen Directory',
                subcity_users: 'Subcity Users',
                tenants: 'Tenants',
                system_users: 'System Users',
                system_settings: 'System Settings',

                // Dashboard and common terms
                total_citizens: 'Total Citizens',
                profile: 'Profile',
                settings: 'Settings',

                // Dashboard Statistics Cards
                total_registered_citizens: 'Total Registered Citizens',
                new_registrations: 'New Registrations',
                pending_approvals: 'Pending Approvals',
                flagged_cases: 'Flagged Cases',
                approved_cards: 'Approved ID Cards',
                expiring_soon: 'Expiring Soon',
                expired_ids: 'Expired IDs',
                migration_activity: 'Migration Activity',
                this_month: 'This month',
                awaiting_review: 'Awaiting review',
                require_attention: 'Require attention',
                ready_for_printing: 'Ready for printing',
                next_30_days: 'Next 30 days',
                over_30_days: 'Over 30 days',

                // Chart and Analytics
                population_by_ketena: 'Population by Ketena',
                age_group_distribution: 'Age Group Distribution',
                monthly_registration_trends: 'Monthly Registration Trends',
                top_3_populated_ketenas: 'Top 3 Populated Ketenas',
                gender_ratio: 'Gender Ratio',
                id_status_summary: 'ID Status Summary',
                recent_citizens: 'Recent Citizens',
                recent_id_card_activity: 'Recent ID Card Activity',
                no_data_available: 'No data available',

                // User menu items
                user_profile: 'Profile',

                // Additional navigation items
                users: 'Users',
                idcards: 'ID Cards',
                clearances: 'Clearances',
                transfers: 'Transfers',

                // Authentication
                login: 'Login',
                logout: 'Logout',
                username: 'Username',
                password: 'Password',
                email: 'Email',
                first_name: 'First Name',
                last_name: 'Last Name',
                sign_in: 'Sign In',
                register: 'Register',
                email_username: 'Email / Username',
                remember_me: 'Remember Me',
                forgot_password: 'Forgot password?',
                tenant_redirect_notice: 'Tenant users will be automatically directed to their tenant based on the domain.',

                // Authentication - Register Page
                organization_name: 'Organization Name',
                confirm_password: 'Confirm Password',
                create_password: 'Create a password',
                confirm_your_password: 'Confirm your password',
                registration_successful: 'Registration successful! You will be redirected to the login page.',
                already_have_account: 'Already have an account?',

                // Authentication - Forgot Password
                forgot_password_title: 'Forgot Password',
                password_reset_instructions: 'Enter your email address and we\'ll send you instructions to reset your password.',
                send_reset_link: 'Send Reset Link',
                password_reset_sent: 'Password reset instructions have been sent to your email.',
                back_to_login: 'Back to Login',
                remember_your_password: 'Remember your password?',

                // User Management
                user_management: 'User Management',
                create_user: 'Create User',
                edit_user: 'Edit User',
                user_profile: 'User Profile',
                user_list: 'User List',
                role: 'Role',
                tenant: 'Tenant',
                is_active: 'Is Active',
                user_created_successfully: 'User created successfully!',
                user_updated_successfully: 'User updated successfully!',

                // Citizen Management
                add_citizen: 'Add Citizen',
                edit_citizen: 'Edit Citizen',
                delete_citizen: 'Delete Citizen',
                citizen_registration: 'Citizen Registration',
                full_name: 'Full Name',
                middle_name: 'Middle Name',
                date_of_birth: 'Date of Birth',
                gender: 'Gender',
                male: 'Male',
                female: 'Female',
                phone_number: 'Phone Number',
                address: 'Address',
                nationality: 'Nationality',
                ethiopian: 'Ethiopian',
                marital_status: 'Marital Status',
                occupation: 'Occupation',
                blood_type: 'Blood Type',
                house_number: 'House Number',
                street: 'Street',
                ketena: 'Ketena',
                religion: 'Religion',
                citizen_status: 'Citizen Status',
                emergency_contact: 'Emergency Contact',
                emergency_contact_name: 'Emergency Contact Name',
                emergency_contact_phone: 'Emergency Contact Phone',
                emergency_contact_relation: 'Relationship',
                spouse_information: 'Spouse Information',
                has_spouse: 'Has Spouse',
                spouse_name: 'Spouse Name',
                spouse_first_name: 'Spouse First Name',
                children_information: 'Children Information',
                number_of_children: 'Number of Children',

                // ID Card Management
                generate_id_card: 'Generate ID Card',
                print_id_card: 'Print ID Card',
                id_card_status: 'ID Card Status',
                id_card_services: 'ID Card Services',
                id_card_print_preview: 'ID Card Print Preview',
                id_card_list: 'ID Card List',
                id_card_create: 'Create ID Card',
                print_preview: 'Print Preview',
                printing_queue: 'Printing Queue',

                // Status Types
                pending: 'Pending',
                approved: 'Approved',
                rejected: 'Rejected',
                printed: 'Printed',
                active: 'Active',
                inactive: 'Inactive',
                expired: 'Expired',

                // Biometric Capture
                capture_fingerprint: 'Capture Fingerprint',
                left_thumb: 'Left Thumb',
                right_thumb: 'Right Thumb',
                fingerprint_quality: 'Fingerprint Quality',
                high_quality: 'High Quality',
                medium_quality: 'Medium Quality',
                low_quality: 'Low Quality',
                biometric_data: 'Biometric Data',
                duplicate_detection: 'Duplicate Detection',
                no_duplicates_found: 'No Duplicates Found',
                duplicate_found: 'Duplicate Found',

                // Common Actions
                save: 'Save',
                cancel: 'Cancel',
                submit: 'Submit',
                edit: 'Edit',
                delete: 'Delete',
                view: 'View',
                search: 'Search',
                filter: 'Filter',
                export: 'Export',
                import: 'Import',
                retry: 'Retry',
                back: 'Back',
                next: 'Next',
                previous: 'Previous',
                approve: 'Approve',
                reject: 'Reject',
                print: 'Print',
                close: 'Close',
                confirm: 'Confirm',
                reset: 'Reset',
                refresh: 'Refresh',



                // Form Fields & Labels
                required_field: 'Required Field',
                optional_field: 'Optional Field',
                choose_option: 'Choose an option',
                select_option: 'Select option',
                enter_text: 'Enter text',
                enter_email: 'Enter your email',
                enter_username: 'Enter username',
                choose_username: 'Choose a username',

                // Status Messages
                success: 'Success',
                error: 'Error',
                warning: 'Warning',
                information: 'Information',
                loading: 'Loading...',
                please_wait: 'Please wait...',



                // Form Validation
                field_required: 'This field is required.',
                invalid_email: 'Please enter a valid email address.',
                invalid_phone: 'Please enter a valid phone number.',
                password_min_length: 'Password must be at least 8 characters long.',

                // Language Settings
                language: 'Language',
                change_language: 'Change Language',
                language_changed: 'Language changed successfully',

                // System Messages
                system_ready: 'System is ready',
                connection_established: 'Connection established',
                connection_failed: 'Connection failed',
                device_connected: 'Device connected',
                device_not_found: 'Device not found',

                // Extended Status Messages
                processing: 'Processing...',
                completed: 'Completed',
                failed: 'Failed',

                // Extended Form Validation Messages
                passwords_must_match: 'Passwords must match',
                enter_valid_email: 'Enter a valid email',
                username_required: 'Username is required',
                first_name_required: 'First name is required',
                last_name_required: 'Last name is required',
                role_required: 'Role is required',
                tenant_required: 'Tenant is required',
                please_fill_required_fields: 'Please fill in all required fields',

                // Dashboard
                dashboard_title: 'Dashboard',
                total_citizens: 'Total Citizens',
                total_id_cards: 'Total ID Cards',
                pending_approvals: 'Pending Approvals',
                approved_cards: 'Approved Cards',
                recent_citizens: 'Recent Citizens',
                recent_id_cards: 'Recent ID Cards',
                statistics: 'Statistics',
                overview: 'Overview',

                // Tenant Management
                tenant_management: 'Tenant Management',
                tenant_list: 'Tenant List',
                create_tenant: 'Create Tenant',
                tenant_name: 'Tenant Name',
                tenant_type: 'Tenant Type',

                // Common UI Elements
                placeholder_text: 'Placeholder text',
                no_data_available: 'No data available',
                no_results_found: 'No results found',
                page_not_found: 'Page not found',
                access_denied: 'Access denied',
                unauthorized: 'Unauthorized',

                // Date & Time
                date: 'Date',
                time: 'Time',
                today: 'Today',
                yesterday: 'Yesterday',
                tomorrow: 'Tomorrow',

                // File Operations
                upload: 'Upload',
                download: 'Download',
                file_uploaded: 'File uploaded successfully',
                file_upload_failed: 'File upload failed',

                // Pagination
                page: 'Page',
                of: 'of',
                items_per_page: 'Items per page',
                showing_results: 'Showing {start} to {end} of {total} results',

                // Confirmation Messages
                confirm_delete: 'Are you sure you want to delete this item?',
                confirm_action: 'Are you sure you want to perform this action?',
                action_cannot_be_undone: 'This action cannot be undone.',

                // Placeholder Content
                enter_your_email: 'Enter your email',
                admin_goid_com: 'admin@goid.<NAME_EMAIL>',

                // Dashboard Statistics Cards
                total_registered_citizens: 'Total Registered Citizens',
                new_registrations: 'New Registrations',
                pending_approvals: 'Pending Approvals',
                flagged_cases: 'Flagged Cases',
                approved_cards: 'Approved ID Cards',
                expiring_soon: 'Expiring Soon',
                expired_ids: 'Expired IDs',
                migration_activity: 'Migration Activity',
                this_month: 'This month',
                awaiting_review: 'Awaiting review',
                require_attention: 'Require attention',
                ready_for_printing: 'Ready for printing',
                next_30_days: 'Next 30 days',
                over_30_days: 'Over 30 days',

                // Chart and Analytics
                population_by_ketena: 'Population by Ketena',
                age_group_distribution: 'Age Group Distribution',
                monthly_registration_trends: 'Monthly Registration Trends',
                top_3_populated_ketenas: 'Top 3 Populated Ketenas',
                gender_ratio: 'Gender Ratio',
                id_status_summary: 'ID Status Summary',
                recent_citizens: 'Recent Citizens',
                recent_id_card_activity: 'Recent ID Card Activity',
                no_data_available: 'No data available',

                // Specific Action Buttons
                generate_printable_book: 'Generate Printable Book',
                search_citizens: 'Search Citizens',
                clear_filters: 'Clear Filters',
                submit_for_approval: 'Submit for Approval',
                kebele_approve: 'Kebele Approve',
                subcity_approve: 'Subcity Approve',
                reset_to_draft: 'Reset to Draft',
                print_preview: 'Print Preview',
                print_front: 'Print Front',
                print_back: 'Print Back',
                photo_viewer: 'Photo Viewer',
                front_side: 'Front Side',
                back_side: 'Back Side',

                // Status and States
                active: 'Active',
                inactive: 'Inactive',
                pending: 'Pending',
                approved: 'Approved',
                rejected: 'Rejected',
                draft: 'Draft',
                completed: 'Completed',
                in_progress: 'In Progress',
                fully_approved: 'Fully Approved - Ready for Printing',
                kebele_approved: 'Kebele Approved - Pending Subcity Approval',
                pending_kebele_approval: 'Pending Kebele Approval',
                printed: 'Printed',
                issued: 'Issued',

                // Form Labels and Placeholders
                all_sub_cities: 'All Sub-Cities',
                all_genders: 'All Genders',
                all_ages: 'All Ages',
                sub_city: 'Sub-City',
                age_group: 'Age Group',
                name_id_phone_email: 'Name, ID, Phone, Email...',
                complete_citizen_directory: 'Complete citizen directory for the city',
                no_citizen_data_available: 'No citizen data available',
                no_citizens_found: 'No citizens found',
                try_adjusting_filters: 'Try adjusting your filters or search criteria',

                // Table Headers
                photo: 'Photo',
                name: 'Name',
                id_number: 'ID Number',
                phone: 'Phone',
                location: 'Location',
                gender: 'Gender',
                actions: 'Actions',
                parent: 'Parent',
                schema_name: 'Schema Name',
                created_on: 'Created On',
                citizen_name: 'Citizen Name',
                issue_date: 'Issue Date',
                expiry_date: 'Expiry Date',

                // Age Groups
                age_18_30: '18-30 years',
                age_31_50: '31-50 years',
                age_51_plus: '51+ years',

                // Additional Dashboard Terms
                sub_cities: 'Sub-Cities',
                total_registered_citizens_18_plus: 'Total Registered Citizens (18+)',
                total_citizens_in_subcity: 'Total Citizens in Subcity',
                id_status_active: 'ID Status Active',
                migration_balance: 'Migration Balance',

                // Additional Chart Titles
                blood_type_distribution: 'Blood Type Distribution',
                new_registrations_daily: 'New Registrations This Month (Daily)',
                expiring_ids_next_30_days: 'Expiring IDs in Next 30 Days',
                migration_summary: 'Migration Summary',
                disability_status_distribution: 'Disability Status Distribution',
                registration_trend_12_months: 'Registration Trend (Last 12 Months)',
                gender_distribution: 'Gender Distribution',

                // ID Card Related
                id_card_preview: 'ID Card Preview',
                id_card_information: 'ID Card Information',
                card_status: 'Card Status',
                security_features: 'Security Features',
                approval_information: 'Approval Information',
                approved_by: 'Approved By',
                approved_at: 'Approved At',
                approval_pattern: 'Approval Pattern',
                rejection_reason: 'Rejection Reason',
                created_by: 'Created By',
                created_at: 'Created At',
                not_issued_yet: 'Not issued yet',
                not_set: 'Not set',
                unknown_citizen: 'Unknown Citizen',
                unknown: 'Unknown',

                // Dialog and Modal
                kebele_approval: 'Kebele Approval',
                subcity_approval: 'Subcity Approval',
                comment_optional: 'Comment (Optional)',
                reason_for_rejection: 'Reason for Rejection',

                // Citizen Management
                register_new_citizen: 'Register New Citizen',
                citizen_details: 'Citizen Details',
                citizen_registered_successfully: 'Citizen registered successfully!',
                search_citizens_placeholder: 'Search citizens by name, ID, or phone number...',
                no_amharic_name: 'No Amharic name',
                resident: 'Resident',
                personal_info: 'Personal Info',
                contact_location: 'Contact & Location',
                family: 'Family',
                employment: 'Employment',
                documents: 'Documents',
                personal_information: 'Personal Information',
                create_id_card: 'Create ID Card',
                citizens_management: 'Citizens Management',
                total_citizens: 'Total Citizens',
                with_id_cards: 'With ID Cards',
                filter: 'Filter',
                refresh_list: 'Refresh List',
                citizens_found: '{count} citizens found',
                kebeles: 'Kebeles',
                male: 'Male',
                female: 'Female'
            },

            am: {
                // Navigation
                dashboard: 'ዳሽቦርድ',
                dashboard_welcome: 'ወደ የመታወቂያ አስተዳደር ዳሽቦርድዎ እንኳን በደህና መጡ',
                citizens: 'ዜጎች',
                id_cards: 'መታወቂያ ካርዶች',

                // Dashboard Statistics Cards
                total_citizens: 'ጠቅላላ ዜጎች',
                total_registered_citizens: 'ጠቅላላ የተመዘገቡ ዜጎች',
                new_registrations: 'አዲስ ምዝገባዎች',
                pending_approvals: 'በመጠባበቅ ላይ ያሉ ፈቃዶች',
                flagged_cases: 'የተጠቆሙ ጉዳዮች',
                approved_cards: 'የተፈቀዱ መታወቂያ ካርዶች',
                expiring_soon: 'በቅርቡ የሚያልቁ',
                expired_ids: 'ጊዜያቸው ያለፈ መታወቂያዎች',
                migration_activity: 'የስደት እንቅስቃሴ',
                this_month: 'በዚህ ወር',
                awaiting_review: 'ለግምገማ በመጠባበቅ ላይ',
                require_attention: 'ትኩረት የሚፈልጉ',
                ready_for_printing: 'ለህትመት ዝግጁ',
                next_30_days: 'ቀጣዮቹ 30 ቀናት',
                over_30_days: 'ከ30 ቀናት በላይ',

                // Chart and Analytics
                population_by_ketena: 'በቀጠና የህዝብ ብዛት',
                age_group_distribution: 'የእድሜ ቡድን ስርጭት',
                monthly_registration_trends: 'ወርሃዊ የምዝገባ አዝማሚያ',
                top_3_populated_ketenas: 'ከፍተኛ ህዝብ ያላቸው 3 ቀጠናዎች',
                gender_ratio: 'የጾታ ሬሾ',
                id_status_summary: 'የመታወቂያ ሁኔታ ማጠቃለያ',
                recent_citizens: 'የቅርብ ጊዜ ዜጎች',
                recent_id_card_activity: 'የቅርብ ጊዜ መታወቂያ ካርድ እንቅስቃሴ',
                no_data_available: 'ምንም መረጃ የለም',
                clearance: 'ፍቃድ',
                transfer: 'ዝውውር',
                print_queue: 'የህትመት ወረፋ',
                service_requests: 'የአገልግሎት ጥያቄዎች',
                kebele_users: 'የቀበሌ ተጠቃሚዎች',
                reports: 'ሪፖርቶች',
                citizen_directory: 'የዜጎች ማውጫ',
                subcity_users: 'የንዑስ ከተማ ተጠቃሚዎች',
                tenants: 'ተከራዮች',
                system_users: 'የስርዓት ተጠቃሚዎች',
                system_settings: 'የስርዓት ቅንብሮች',

                // Dashboard and common terms
                total_citizens: 'ጠቅላላ ዜጎች',
                profile: 'መገለጫ',
                settings: 'ቅንብሮች',

                // User menu items
                user_profile: 'መገለጫ',

                // Additional navigation items
                users: 'ተጠቃሚዎች',
                idcards: 'መታወቂያ ካርዶች',
                clearances: 'ፍቃዶች',
                transfers: 'ዝውውሮች',

                // Authentication - Login Page
                login: 'ግባ',
                logout: 'ውጣ',
                sign_in: 'ግባ',
                register: 'ተመዝገብ',
                username: 'የተጠቃሚ ስም',
                password: 'የይለፍ ቃል',
                email: 'ኢሜይል',
                first_name: 'ስም',
                last_name: 'የአባት ስም',
                email_username: 'ኢሜይል / የተጠቃሚ ስም',
                remember_me: 'አስታውሰኝ',
                forgot_password: 'የይለፍ ቃል ረሳኸው?',
                tenant_redirect_notice: 'የተከራይ ተጠቃሚዎች በራስ-ሰር ወደ የራሳቸው ተከራይ በዶሜይን ላይ ተመርተው ይሄዳሉ።',

                // Authentication - Register Page
                organization_name: 'የድርጅት ስም',
                confirm_password: 'የይለፍ ቃል ያረጋግጡ',
                create_password: 'የይለፍ ቃል ይፍጠሩ',
                confirm_your_password: 'የይለፍ ቃልዎን ያረጋግጡ',
                registration_successful: 'ምዝገባ ተሳክቷል! ወደ ግባ ገጽ ይወሰዳሉ።',
                already_have_account: 'መለያ አለዎት?',

                // Authentication - Forgot Password
                forgot_password_title: 'የይለፍ ቃል ረሳሁ',
                password_reset_instructions: 'የኢሜይል አድራሻዎን ያስገቡ እና የይለፍ ቃል እንደገና የማስተካከያ መመሪያዎችን እንልክልዎታለን።',
                send_reset_link: 'የማስተካከያ ሊንክ ላክ',
                password_reset_sent: 'የይለፍ ቃል እንደገና የማስተካከያ መመሪያዎች ወደ ኢሜይልዎ ተልከዋል።',
                back_to_login: 'ወደ ግባ ተመለስ',
                remember_your_password: 'የይለፍ ቃልዎን አስታወሱ?',

                // User Management
                user_management: 'የተጠቃሚ አሰራር',
                create_user: 'ተጠቃሚ ፍጠር',
                edit_user: 'ተጠቃሚን አርም',
                user_profile: 'የተጠቃሚ መገለጫ',
                user_list: 'የተጠቃሚዎች ዝርዝር',
                role: 'ሚና',
                tenant: 'ተከራይ',
                is_active: 'ነቃ ነው',
                user_created_successfully: 'ተጠቃሚ በተሳካ ሁኔታ ተፈጥሯል!',
                user_updated_successfully: 'ተጠቃሚ በተሳካ ሁኔታ ተዘምኗል!',

                // Citizen Management
                add_citizen: 'ዜጋ ጨምር',
                edit_citizen: 'ዜጋን አርም',
                delete_citizen: 'ዜጋን ሰርዝ',
                citizen_registration: 'የዜጋ ምዝገባ',
                full_name: 'ሙሉ ስም',
                middle_name: 'የአያት ስም',
                date_of_birth: 'የተወለደበት ቀን',
                gender: 'ጾታ',
                male: 'ወንድ',
                female: 'ሴት',
                phone_number: 'ስልክ ቁጥር',
                address: 'አድራሻ',
                nationality: 'ዜግነት',
                ethiopian: 'ኢትዮጵያዊ',
                marital_status: 'የጋብቻ ሁኔታ',
                occupation: 'ሙያ',
                blood_type: 'የደም ዓይነት',
                house_number: 'የቤት ቁጥር',
                street: 'መንገድ',
                ketena: 'ከተና',
                religion: 'ሃይማኖት',
                citizen_status: 'የዜጋ ሁኔታ',
                emergency_contact: 'ጥሪ የሚደረግበት ሰው',
                emergency_contact_name: 'የጥሪ ሰው ስም',
                emergency_contact_phone: 'የጥሪ ሰው ስልክ',
                emergency_contact_relation: 'ዝምድና',
                spouse_information: 'የትዳር ጓደኛ መረጃ',
                has_spouse: 'ትዳር ጓደኛ አለ',
                spouse_name: 'የትዳር ጓደኛ ስም',
                spouse_first_name: 'የትዳር ጓደኛ ስም',
                children_information: 'የልጆች መረጃ',
                number_of_children: 'የልጆች ቁጥር',

                // ID Card Management
                generate_id_card: 'መታወቂያ ካርድ ፍጠር',
                print_id_card: 'መታወቂያ ካርድ አትም',
                id_card_status: 'የመታወቂያ ካርድ ሁኔታ',
                id_card_services: 'የመታወቂያ ካርድ አገልግሎቶች',
                id_card_print_preview: 'የመታወቂያ ካርድ የህትመት ቅድመ እይታ',
                id_card_list: 'የመታወቂያ ካርዶች ዝርዝር',
                id_card_create: 'መታወቂያ ካርድ ፍጠር',
                print_preview: 'የህትመት ቅድመ እይታ',
                printing_queue: 'የህትመት ወረፋ',

                // Status Types
                pending: 'በመጠባበቅ ላይ',
                approved: 'ጸድቋል',
                rejected: 'ተቀባይነት አላገኘም',
                printed: 'ታትሟል',
                active: 'ንቁ',
                inactive: 'ንቁ አይደለም',
                expired: 'ጊዜው አልፏል',

                // Biometric Capture
                capture_fingerprint: 'የጣት አሻራ ያንሱ',
                left_thumb: 'የግራ አውራ ጣት',
                right_thumb: 'የቀኝ አውራ ጣት',
                fingerprint_quality: 'የጣት አሻራ ጥራት',
                high_quality: 'ከፍተኛ ጥራት',
                medium_quality: 'መካከለኛ ጥራት',
                low_quality: 'ዝቅተኛ ጥራት',
                biometric_data: 'ባዮሜትሪክ መረጃ',
                duplicate_detection: 'ተደጋጋሚ ፍለጋ',
                no_duplicates_found: 'ተደጋጋሚ አልተገኘም',
                duplicate_found: 'ተደጋጋሚ ተገኝቷል',

                // Common Actions
                save: 'አስቀምጥ',
                cancel: 'ሰርዝ',
                submit: 'አስገባ',
                edit: 'አርም',
                delete: 'ሰርዝ',
                view: 'ይመልከቱ',
                search: 'ፈልግ',
                filter: 'ማጣሪያ',
                export: 'ወደ ውጭ ላክ',
                import: 'ከውጭ አምጣ',
                retry: 'እንደገና ሞክር',
                back: 'ተመለስ',
                next: 'ቀጣይ',
                previous: 'ቀዳሚ',
                approve: 'ፈቅድ',
                reject: 'ውድቅ አድርግ',
                print: 'አትም',
                close: 'ዝጋ',
                confirm: 'አረጋግጥ',
                reset: 'እንደገና አስጀምር',
                refresh: 'አድስ',



                // Form Fields & Labels
                required_field: 'አስፈላጊ መስክ',
                optional_field: 'አማራጭ መስክ',
                choose_option: 'አንድ አማራጭ ይምረጡ',
                select_option: 'አማራጭ ይምረጡ',
                enter_text: 'ጽሑፍ ያስገቡ',
                enter_email: 'ኢሜይልዎን ያስገቡ',
                enter_password: 'የይለፍ ቃልዎን ያስገቡ',
                enter_username: 'የተጠቃሚ ስም ያስገቡ',
                choose_username: 'የተጠቃሚ ስም ይምረጡ',

                // Status Messages
                success: 'ተሳክቷል',
                error: 'ስህተት',
                warning: 'ማስጠንቀቂያ',
                information: 'መረጃ',
                loading: 'በመጫን ላይ...',
                please_wait: 'እባክዎ ይጠብቁ...',
                processing: 'በሂደት ላይ...',
                completed: 'ተጠናቅቋል',
                failed: 'አልተሳካም',

                // Form Validation Messages
                field_required: 'ይህ መስክ አስፈላጊ ነው።',
                invalid_email: 'እባክዎ ትክክለኛ ኢሜይል አድራሻ ያስገቡ।',
                invalid_phone: 'እባክዎ ትክክለኛ ስልክ ቁጥር ያስገቡ።',
                password_min_length: 'የይለፍ ቃል ቢያንስ 8 ቁምፊዎች ሊኖሩት ይገባል।',
                passwords_must_match: 'የይለፍ ቃሎች መመሳሰል አለባቸው',
                enter_valid_email: 'ትክክለኛ ኢሜይል ያስገቡ',
                username_required: 'የተጠቃሚ ስም አስፈላጊ ነው',
                password_required: 'የይለፍ ቃል አስፈላጊ ነው',
                first_name_required: 'ስም አስፈላጊ ነው',
                last_name_required: 'የአባት ስም አስፈላጊ ነው',
                role_required: 'ሚና አስፈላጊ ነው',
                tenant_required: 'ተከራይ አስፈላጊ ነው',
                please_fill_required_fields: 'እባክዎ ሁሉንም አስፈላጊ መስኮች ይሙሉ',
                login_failed_check_credentials: 'መግባት አልተሳካም። እባክዎ ምስክርዎትዎችን ያረጋግጡ።',
                registration_failed_try_again: 'ምዝገባ አልተሳካም። እባክዎ እንደገና ይሞክሩ።',
                password_reset_failed: 'የይለፍ ቃል እንደገና ማስተካከያ ኢሜይል መላክ አልተሳካም። እባክዎ እንደገና ይሞክሩ።',



                // Dashboard
                dashboard_title: 'ዳሽቦርድ',
                total_citizens: 'አጠቃላይ ዜጎች',
                total_id_cards: 'አጠቃላይ መታወቂያ ካርዶች',
                pending_approvals: 'በመጠባበቅ ላይ ያሉ ማጽደቂያዎች',
                approved_cards: 'የጸደቁ ካርዶች',
                recent_citizens: 'የቅርብ ጊዜ ዜጎች',
                recent_id_cards: 'የቅርብ ጊዜ መታወቂያ ካርዶች',
                statistics: 'ስታቲስቲክስ',
                overview: 'አጠቃላይ እይታ',

                // Tenant Management
                tenant_management: 'የተከራይ አሰራር',
                tenant_list: 'የተከራዮች ዝርዝር',
                create_tenant: 'ተከራይ ፍጠር',
                tenant_name: 'የተከራይ ስም',
                tenant_type: 'የተከራይ ዓይነት',

                // Common UI Elements
                placeholder_text: 'ምሳሌ ጽሑፍ',
                no_data_available: 'መረጃ የለም',
                no_results_found: 'ውጤት አልተገኘም',
                page_not_found: 'ገጽ አልተገኘም',
                access_denied: 'መዳረሻ ተከልክሏል',
                unauthorized: 'ፍቃድ የለም',

                // Language Settings
                language: 'ቋንቋ',
                change_language: 'ቋንቋ ቀይር',
                language_changed: 'ቋንቋ በተሳካ ሁኔታ ተቀይሯል',

                // System Messages
                system_ready: 'ስርዓቱ ዝግጁ ነው',
                connection_established: 'ግንኙነት ተመስርቷል',
                connection_failed: 'ግንኙነት አልተሳካም',
                device_connected: 'መሳሪያ ተገናኝቷል',
                device_not_found: 'መሳሪያ አልተገኘም',

                // Date & Time
                date: 'ቀን',
                time: 'ሰዓት',
                today: 'ዛሬ',
                yesterday: 'ትላንት',
                tomorrow: 'ነገ',

                // File Operations
                upload: 'ከፍ አድርግ',
                download: 'አውርድ',
                file_uploaded: 'ፋይል በተሳካ ሁኔታ ተከፍቷል',
                file_upload_failed: 'ፋይል ከፍ ማድረግ አልተሳካም',

                // Pagination
                page: 'ገጽ',
                of: 'ከ',
                items_per_page: 'በገጽ ያሉ ነገሮች',
                showing_results: 'ከ {total} ውጤቶች {start} እስከ {end} በማሳየት ላይ',

                // Confirmation Messages
                confirm_delete: 'ይህንን ነገር መሰረዝ እርግጠኛ ነዎት?',
                confirm_action: 'ይህንን ድርጊት ማከናወን እርግጠኛ ነዎት?',
                action_cannot_be_undone: 'ይህ ድርጊት መልሶ ሊቀየር አይችልም።',

                // Placeholder Content
                enter_your_email: 'ኢሜይልዎን ያስገቡ',
                admin_goid_com: '<EMAIL> ወይም <EMAIL>',

                // Dashboard Statistics Cards
                total_registered_citizens: 'ጠቅላላ የተመዘገቡ ዜጎች',
                new_registrations: 'አዲስ ምዝገባዎች',
                pending_approvals: 'በመጠባበቅ ላይ ያሉ ፈቃዶች',
                flagged_cases: 'የተጠቆሙ ጉዳዮች',
                approved_cards: 'የተፈቀዱ መታወቂያ ካርዶች',
                expiring_soon: 'በቅርቡ የሚያልቁ',
                expired_ids: 'ጊዜያቸው ያለፈ መታወቂያዎች',
                migration_activity: 'የስደት እንቅስቃሴ',
                this_month: 'በዚህ ወር',
                awaiting_review: 'ለግምገማ በመጠባበቅ ላይ',
                require_attention: 'ትኩረት የሚፈልጉ',
                ready_for_printing: 'ለህትመት ዝግጁ',
                next_30_days: 'ቀጣዮቹ 30 ቀናት',
                over_30_days: 'ከ30 ቀናት በላይ',

                // Chart and Analytics
                population_by_ketena: 'በቀጠና የህዝብ ብዛት',
                age_group_distribution: 'የእድሜ ቡድን ስርጭት',
                monthly_registration_trends: 'ወርሃዊ የምዝገባ አዝማሚያ',
                top_3_populated_ketenas: 'ከፍተኛ ህዝብ ያላቸው 3 ቀጠናዎች',
                gender_ratio: 'የጾታ ሬሾ',
                id_status_summary: 'የመታወቂያ ሁኔታ ማጠቃለያ',
                recent_citizens: 'የቅርብ ጊዜ ዜጎች',
                recent_id_card_activity: 'የቅርብ ጊዜ መታወቂያ ካርድ እንቅስቃሴ',
                no_data_available: 'ምንም መረጃ የለም',

                // Specific Action Buttons
                generate_printable_book: 'ለህትመት የሚሆን መጽሐፍ ፍጠር',
                search_citizens: 'ዜጎችን ፈልግ',
                clear_filters: 'ማጣሪያዎችን አጽዳ',
                submit_for_approval: 'ለፈቃድ አስገባ',
                kebele_approve: 'ቀበሌ ፈቃድ',
                subcity_approve: 'ክ/ከተማ ፈቃድ',
                reset_to_draft: 'ወደ ረቂቅ መልስ',
                print_preview: 'የህትመት ቅድመ እይታ',
                print_front: 'ፊት ለፊት አትም',
                print_back: 'ጀርባ አትም',
                photo_viewer: 'ፎቶ መመልከቻ',
                front_side: 'ፊት ለፊት',
                back_side: 'ጀርባ',

                // Status and States
                active: 'ንቁ',
                inactive: 'ንቁ ያልሆነ',
                pending: 'በመጠባበቅ ላይ',
                approved: 'የተፈቀደ',
                rejected: 'የተከለከለ',
                draft: 'ረቂቅ',
                completed: 'ተጠናቅቋል',
                in_progress: 'በሂደት ላይ',
                fully_approved: 'ሙሉ በሙሉ የተፈቀደ - ለህትመት ዝግጁ',
                kebele_approved: 'ቀበሌ የተፈቀደ - ክ/ከተማ ፈቃድ በመጠባበቅ ላይ',
                pending_kebele_approval: 'የቀበሌ ፈቃድ በመጠባበቅ ላይ',
                printed: 'ተህትሟል',
                issued: 'ተሰጥቷል',

                // Form Labels and Placeholders
                all_sub_cities: 'ሁሉም ክ/ከተማዎች',
                all_genders: 'ሁሉም ጾታዎች',
                all_ages: 'ሁሉም እድሜዎች',
                sub_city: 'ክ/ከተማ',
                age_group: 'የእድሜ ቡድን',
                name_id_phone_email: 'ስም፣ መታወቂያ፣ ስልክ፣ ኢሜይል...',
                complete_citizen_directory: 'ሙሉ የከተማ ዜጎች ማውጫ',
                no_citizen_data_available: 'ምንም የዜጋ መረጃ የለም',
                no_citizens_found: 'ምንም ዜጎች አልተገኙም',
                try_adjusting_filters: 'ማጣሪያዎችዎን ወይም የፍለጋ መስፈርቶችዎን ማስተካከል ይሞክሩ',

                // Table Headers
                photo: 'ፎቶ',
                name: 'ስም',
                id_number: 'መታወቂያ ቁጥር',
                phone: 'ስልክ',
                location: 'አካባቢ',
                gender: 'ጾታ',
                actions: 'እርምጃዎች',
                parent: 'ወላጅ',
                schema_name: 'የስኪማ ስም',
                created_on: 'የተፈጠረበት ቀን',
                citizen_name: 'የዜጋ ስም',
                issue_date: 'የመውጫ ቀን',
                expiry_date: 'የማለቂያ ቀን',

                // Age Groups
                age_18_30: '18-30 ዓመት',
                age_31_50: '31-50 ዓመት',
                age_51_plus: '51+ ዓመት',

                // Additional Dashboard Terms
                sub_cities: 'ክ/ከተማዎች',
                total_registered_citizens_18_plus: 'ጠቅላላ የተመዘገቡ ዜጎች (18+)',
                total_citizens_in_subcity: 'በክ/ከተማ ውስጥ ያሉ ጠቅላላ ዜጎች',
                id_status_active: 'ንቁ የመታወቂያ ሁኔታ',
                migration_balance: 'የስደት ሚዛን',

                // Additional Chart Titles
                blood_type_distribution: 'የደም ዓይነት ስርጭት',
                new_registrations_daily: 'በዚህ ወር አዲስ ምዝገባዎች (ዕለታዊ)',
                expiring_ids_next_30_days: 'በቀጣዮቹ 30 ቀናት የሚያልቁ መታወቂያዎች',
                migration_summary: 'የስደት ማጠቃለያ',
                disability_status_distribution: 'የአካል ጉዳት ሁኔታ ስርጭት',
                registration_trend_12_months: 'የምዝገባ አዝማሚያ (ባለፉት 12 ወራት)',
                gender_distribution: 'የጾታ ስርጭት',

                // ID Card Related
                id_card_preview: 'የመታወቂያ ካርድ ቅድመ እይታ',
                id_card_information: 'የመታወቂያ ካርድ መረጃ',
                card_status: 'የካርድ ሁኔታ',
                security_features: 'የደህንነት ባህሪያት',
                approval_information: 'የፈቃድ መረጃ',
                approved_by: 'የፈቀደው',
                approved_at: 'የተፈቀደበት ቀን',
                approval_pattern: 'የፈቃድ ዘዴ',
                rejection_reason: 'የመከልከል ምክንያት',
                created_by: 'የፈጠረው',
                created_at: 'የተፈጠረበት ቀን',
                not_issued_yet: 'ገና አልተሰጠም',
                not_set: 'አልተዘጋጀም',
                unknown_citizen: 'ያልታወቀ ዜጋ',
                unknown: 'ያልታወቀ',

                // Dialog and Modal
                kebele_approval: 'የቀበሌ ፈቃድ',
                subcity_approval: 'የክ/ከተማ ፈቃድ',
                comment_optional: 'አስተያየት (አማራጭ)',
                reason_for_rejection: 'የመከልከል ምክንያት',

                // Citizen Management
                register_new_citizen: 'አዲስ ዜጋ ምዝገባ',
                citizen_details: 'የዜጋ ዝርዝር',
                citizen_registered_successfully: 'ዜጋ በተሳካ ሁኔታ ተመዝግቧል!',
                search_citizens_placeholder: 'ዜጎችን በስም፣ መታወቂያ ወይም ስልክ ቁጥር ይፈልጉ...',
                no_amharic_name: 'የአማርኛ ስም የለም',
                resident: 'ነዋሪ',
                personal_info: 'የግል መረጃ',
                contact_location: 'ግንኙነት እና አካባቢ',
                family: 'ቤተሰብ',
                employment: 'ስራ',
                documents: 'ሰነዶች',
                personal_information: 'የግል መረጃ',
                create_id_card: 'መታወቂያ ካርድ ፍጠር',
                citizens_management: 'የዜጎች አስተዳደር',
                total_citizens: 'ጠቅላላ ዜጎች',
                with_id_cards: 'መታወቂያ ካርድ ያላቸው',
                filter: 'ማጣሪያ',
                refresh_list: 'ዝርዝሩን አድስ',
                citizens_found: '{count} ዜጎች ተገኝተዋል',
                kebeles: 'ቀበሌዎች',
                male: 'ወንድ',
                female: 'ሴት'
            },
            om: {
                // Basic Oromo translations
                dashboard: 'Gabatee',
                citizens: 'Lammii',
                id_cards: 'Kaardii Eenyummaa',
                login: 'Seeni',
                logout: 'Ba\'i',
                username: 'Maqaa Fayyadamaa',
                password: 'Jecha Icciitii',
                save: 'Olkaa\'i',
                cancel: 'Dhiisi',
                delete: 'Haqi',
                edit: 'Gulali',
                add: 'Dabali',
                search: 'Barbaadi',
                loading: 'Fe\'aa jira...',
                success: 'Milkaa\'e',
                error: 'Dogoggora',
                yes: 'Eeyyee',
                no: 'Lakki'
            },
            ti: {
                // Basic Tigrinya translations
                dashboard: 'ዳሽቦርድ',
                citizens: 'ዜጋታት',
                id_cards: 'መንነት ካርድ',
                login: 'እቶት',
                logout: 'ወጻኢ',
                username: 'ሽም ተጠቃሚ',
                password: 'መሕለፊ ቃል',
                save: 'ዓቅብ',
                cancel: 'ሰርዝ',
                delete: 'ደምስስ',
                edit: 'ኣርም',
                add: 'ወስኽ',
                search: 'ድለ',
                loading: 'ይጽዕን ኣሎ...',
                success: 'ዓወት',
                error: 'ጌጋ',
                yes: 'እወ',
                no: 'ኣይፋልን'
            },
            so: {
                // Basic Somali translations
                dashboard: 'Shabakadda',
                citizens: 'Muwaadiniinta',
                id_cards: 'Kaararka Aqoonsiga',
                login: 'Gal',
                logout: 'Ka bax',
                username: 'Magaca isticmaalaha',
                password: 'Furaha sirta ah',
                save: 'Kaydi',
                cancel: 'Jooji',
                delete: 'Tirtir',
                edit: 'Wax ka beddel',
                add: 'Ku dar',
                search: 'Raadi',
                loading: 'Waa la rarayo...',
                success: 'Guul',
                error: 'Qalad',
                yes: 'Haa',
                no: 'Maya'
            },
            aa: {
                // Basic Afar translations
                dashboard: 'Gabatee',
                citizens: 'Qafar',
                id_cards: 'Eenyummaa Kaardii',
                login: 'Gal',
                logout: 'Bah',
                username: 'Maqaa',
                password: 'Sirrii',
                save: 'Kaydi',
                cancel: 'Jooji',
                delete: 'Haqi',
                edit: 'Beddeli',
                add: 'Dabali',
                search: 'Barbaadi',
                loading: 'Raarayaa...',
                success: 'Milkaa',
                error: 'Qalad',
                yes: 'Wayyo',
                no: 'Maleey'
            }
        };
    }

    /**
     * Get current language
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * Get available languages
     */
    getAvailableLanguages() {
        return this.availableLanguages;
    }

    /**
     * Set language
     */
    async setLanguage(languageCode) {
        try {
            // Update backend
            await axios.post('/api/common/language/set/', {
                language: languageCode
            });

            // Update local state
            this.currentLanguage = languageCode;
            localStorage.setItem('goid_language', languageCode);

            // Load translations from backend
            await this.loadTranslations();

            return true;
        } catch (error) {
            console.warn('Backend language API not available, using local fallback:', error.message);

            // Fallback to local language change with local translations
            this.currentLanguage = languageCode;
            localStorage.setItem('goid_language', languageCode);

            // Use local translations as fallback - they're already initialized
            // No need to call getLocalTranslations since translations are already available

            return true; // Return true for fallback mode
        }
    }

    /**
    * Load translations from backend
    */
    async loadTranslations() {
    try {
    const response = await axios.get(`/api/common/translations/?language=${this.currentLanguage}`);

    if (response.data && response.data.translations) {
    // Replace local translations with backend ones (backend is source of truth)
    this.translations[this.currentLanguage] = {
    ...this.translations[this.currentLanguage], // Keep local as fallback
    ...response.data.translations // Override with backend translations
    };
      console.log(`🌐 Loaded ${Object.keys(response.data.translations).length} translations for ${this.currentLanguage}`);
      }
    } catch (error) {
      console.warn('Failed to load translations from backend, using local translations:', error);

    // Ensure we have local translations loaded - they're already initialized in constructor
      // Local translations are already available in this.translations
      }
  }

    /**
     * Get translation for a key
     */
    t(key, defaultValue = null) {
        const languageTranslations = this.translations[this.currentLanguage] || this.translations['en'];
        return languageTranslations[key] || defaultValue || key;
    }

    /**
     * Get translation with parameters
     */
    tp(key, params = {}, defaultValue = null) {
        let translation = this.t(key, defaultValue);

        // Replace parameters in translation
        Object.keys(params).forEach(param => {
            translation = translation.replace(`{${param}}`, params[param]);
        });

        return translation;
    }

    /**
     * RTL language configuration
     */
    rtlLanguages = {
        'ar': { name: 'Arabic', direction: 'rtl', align: 'right' },
        'he': { name: 'Hebrew', direction: 'rtl', align: 'right' },
        'fa': { name: 'Persian', direction: 'rtl', align: 'right' },
        'ur': { name: 'Urdu', direction: 'rtl', align: 'right' },
        'ku': { name: 'Kurdish', direction: 'rtl', align: 'right' },
        'ps': { name: 'Pashto', direction: 'rtl', align: 'right' },
        'sd': { name: 'Sindhi', direction: 'rtl', align: 'right' }
    };

    /**
     * Check if current language is RTL
     */
    isRTL(languageCode = null) {
        const lang = languageCode || this.currentLanguage;
        return this.rtlLanguages.hasOwnProperty(lang);
    }

    /**
     * Get language direction
     */
    getDirection(languageCode = null) {
        const lang = languageCode || this.currentLanguage;
        return this.isRTL(lang) ? 'rtl' : 'ltr';
    }

    /**
     * Get text alignment for current language
     */
    getTextAlign(languageCode = null) {
        const lang = languageCode || this.currentLanguage;
        return this.isRTL(lang) ? 'right' : 'left';
    }

    /**
     * Get margin/padding direction helpers
     */
    getDirectionHelpers(languageCode = null) {
        const isRtl = this.isRTL(languageCode);
        return {
            isRTL: isRtl,
            direction: isRtl ? 'rtl' : 'ltr',
            textAlign: isRtl ? 'right' : 'left',
            marginStart: isRtl ? 'marginRight' : 'marginLeft',
            marginEnd: isRtl ? 'marginLeft' : 'marginRight',
            paddingStart: isRtl ? 'paddingRight' : 'paddingLeft',
            paddingEnd: isRtl ? 'paddingLeft' : 'paddingRight',
            borderStart: isRtl ? 'borderRight' : 'borderLeft',
            borderEnd: isRtl ? 'borderLeft' : 'borderRight',
            start: isRtl ? 'right' : 'left',
            end: isRtl ? 'left' : 'right'
        };
    }

    /**
     * Initialize localization service
     */
    async initialize() {
        try {
            // Load current language info from backend
            const response = await axios.get('/api/common/language/info/');

            if (response.data && response.data.current_language) {
                this.currentLanguage = response.data.current_language;
                localStorage.setItem('goid_language', this.currentLanguage);
            }

            // Load translations
            await this.loadTranslations();

            return true;
        } catch (error) {
            console.warn('Failed to initialize localization from backend, using local settings:', error);
            return false;
        }
    }
}

// Create singleton instance
const localizationService = new LocalizationService();

export default localizationService;
