import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  Toolbar,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Avatar,
  Button,
  Card,
  CardContent,
  Grid,
  Tooltip,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  PersonAdd as PersonAddIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Badge as BadgeIcon,
  MoreVert as MoreVertIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Person as PersonIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from '@mui/icons-material';
import { usePermissions } from '../../hooks/usePermissions';

/**
 * Get tenant ID from JWT token
 * @returns {number|null} The tenant ID or null if not found
 */
const getTenantId = () => {
  try {
    // First try to get from localStorage user object
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.tenant_id) {
      return user.tenant_id;
    }

    // Fallback: Get the access token from localStorage and decode it
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      console.warn('No access token found in localStorage');
      return null;
    }

    // Decode the JWT token to get tenant information
    const base64Url = accessToken.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const tokenData = JSON.parse(jsonPayload);
    return tokenData.tenant_id || null;
  } catch (error) {
    console.error('Error getting tenant ID:', error);
    return null;
  }
};

const CitizensList = () => {
  const navigate = useNavigate();
  const { hasPermission } = usePermissions();
  const { user } = useAuth();
  const { t } = useLocalization();

  // State management
  const [citizens, setCitizens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [orderBy, setOrderBy] = useState('created_at');
  const [order, setOrder] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [totalCount, setTotalCount] = useState(0);

  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    male: 0,
    female: 0,
    withIdCards: 0
  });

  useEffect(() => {
    fetchCitizens();
    fetchStats();
  }, [page, rowsPerPage, orderBy, order, searchTerm]);

  const fetchCitizens = async () => {
    try {
      setLoading(true);

      // Get tenant ID dynamically
      const tenantId = getTenantId();
      if (!tenantId) {
        setError('No tenant context found. Please log in again.');
        setLoading(false);
        return;
      }

      const params = {
        page: page + 1,
        page_size: rowsPerPage,
        ordering: order === 'desc' ? `-${orderBy}` : orderBy,
        search: searchTerm,
      };

      console.log('🔍 Fetching citizens for tenant:', tenantId);
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/`, { params });
      setCitizens(response.data.results || []);
      setTotalCount(response.data.count || 0);
      console.log('✅ Citizens fetched:', response.data.results?.length || 0);
    } catch (error) {
      console.error('Failed to fetch citizens:', error);
      setError('Failed to load citizens. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Get tenant ID dynamically
      const tenantId = getTenantId();
      if (!tenantId) {
        console.warn('No tenant context found for stats');
        return;
      }

      console.log('🔍 Fetching stats for tenant:', tenantId);
      const response = await axios.get(`/api/tenants/${tenantId}/citizens/stats/`);
      setStats(response.data);
    } catch (error) {
      console.error('Failed to fetch stats:', error);
      // Don't show error for stats, just log it
    }
  };

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const getGenderIcon = (gender) => {
    switch (gender?.toLowerCase()) {
      case 'male':
        return <MaleIcon color="primary" />;
      case 'female':
        return <FemaleIcon color="secondary" />;
      default:
        return <PersonIcon />;
    }
  };

  const getStatusChip = (status) => {
    const colors = {
      active: 'success',
      inactive: 'default',
      pending: 'warning',
      suspended: 'error',
    };

    // Handle different status types (string, number, boolean)
    const statusString = status ? String(status).toLowerCase() : 'unknown';

    return (
      <Chip
        label={status || 'Unknown'}
        color={colors[statusString] || 'default'}
        size="small"
        variant="outlined"
      />
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const calculateAge = (birthDate) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" sx={{ mb: 1 }}>
            {t('citizens_management', 'Citizens Management')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('citizens_found', '{count} citizens found').replace('{count}', totalCount)}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {hasPermission('register_citizens') && (
            <Button
              variant="contained"
              startIcon={<PersonAddIcon />}
              onClick={() => navigate('/citizens/create')}
              size="large"
              sx={{
                bgcolor: 'success.main',
                '&:hover': { bgcolor: 'success.dark' }
              }}
            >
              {t('register_new_citizen', 'Register New Citizen')}
            </Button>
          )}
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <PersonIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    {t('total_citizens', 'Total Citizens')}
                  </Typography>
                  <Typography variant="h4">
                    {(stats.total || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <MaleIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    Male
                  </Typography>
                  <Typography variant="h4">
                    {(stats.male || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <FemaleIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    Female
                  </Typography>
                  <Typography variant="h4">
                    {(stats.female || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <BadgeIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    With ID Cards
                  </Typography>
                  <Typography variant="h4">
                    {(stats.withIdCards || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <TextField
            placeholder="Search by name, ID number..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300, flexGrow: 1 }}
            size="small"
          />
          <Button
            variant="contained"
            startIcon={<SearchIcon />}
            sx={{
              bgcolor: 'success.main',
              '&:hover': { bgcolor: 'success.dark' }
            }}
          >
            {t('search', 'Search')}
          </Button>
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            sx={{ borderColor: 'success.main', color: 'success.main' }}
          >
            {t('filter', 'Filter')}
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => {
              setSearchTerm('');
              fetchCitizens();
            }}
            sx={{ borderColor: 'success.main', color: 'success.main' }}
          >
            {t('refresh_list', 'Refresh List')}
          </Button>
        </Box>
      </Paper>

      {/* Citizens Table */}
      <Paper>
        <TableContainer>
          <Table stickyHeader>
            <TableHead>
              <TableRow sx={{ bgcolor: 'grey.50' }}>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  {t('photo', 'PHOTO')}
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  {t('id_number', 'ID NUMBER')}
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  <TableSortLabel
                    active={orderBy === 'first_name'}
                    direction={orderBy === 'first_name' ? order : 'asc'}
                    onClick={() => handleRequestSort('first_name')}
                  >
                    {t('name', 'NAME')}
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  {t('gender', 'GENDER')}
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  <TableSortLabel
                    active={orderBy === 'date_of_birth'}
                    direction={orderBy === 'date_of_birth' ? order : 'asc'}
                    onClick={() => handleRequestSort('date_of_birth')}
                  >
                    AGE
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  PHONE
                </TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  ACTIONS
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : citizens.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                    <Typography variant="body1" color="textSecondary">
                      No citizens found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                citizens.map((citizen) => (
                  <TableRow key={citizen.id} hover sx={{ '&:hover': { bgcolor: 'grey.50' } }}>
                    <TableCell sx={{ py: 2 }}>
                      <Avatar
                        src={citizen.photo}
                        alt={citizen.full_name}
                        sx={{
                          width: 48,
                          height: 48,
                          bgcolor: 'grey.300',
                          color: 'white',
                          fontSize: '1.2rem',
                          fontWeight: 'bold'
                        }}
                      >
                        {citizen.first_name?.charAt(0)?.toUpperCase() || 'U'}
                      </Avatar>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{
                          color: 'success.main',
                          fontFamily: 'monospace'
                        }}
                      >
                        {citizen.digital_id || citizen.citizen_id || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Box>
                        <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 0.5 }}>
                          {citizen.full_name || `${citizen.first_name || ''} ${citizen.middle_name || ''} ${citizen.last_name || ''}`.trim()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {citizen.first_name_am || citizen.middle_name_am || citizen.last_name_am ?
                            `${citizen.first_name_am || ''} ${citizen.middle_name_am || ''} ${citizen.last_name_am || ''}`.trim() :
                            'No Amharic name'
                          }
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Chip
                        label={citizen.gender || 'Unknown'}
                        color={citizen.gender === 'male' ? 'info' : citizen.gender === 'female' ? 'secondary' : 'default'}
                        size="small"
                        sx={{
                          fontWeight: 'bold',
                          textTransform: 'capitalize'
                        }}
                      />
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {citizen.date_of_birth ? calculateAge(citizen.date_of_birth) : 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2">
                        {citizen.phone || citizen.phone_number || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell align="center" sx={{ py: 2 }}>
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                        <IconButton
                          onClick={() => navigate(`/citizens/${citizen.id}`)}
                          size="small"
                          sx={{
                            bgcolor: 'primary.main',
                            color: 'white',
                            '&:hover': { bgcolor: 'primary.dark' }
                          }}
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                        {!citizen.has_id_card && hasPermission('create_idcards') && (
                          <IconButton
                            onClick={() => navigate(`/citizens/${citizen.id}/idcards/create`)}
                            size="small"
                            sx={{
                              bgcolor: 'success.main',
                              color: 'white',
                              '&:hover': { bgcolor: 'success.dark' }
                            }}
                          >
                            <BadgeIcon fontSize="small" />
                          </IconButton>
                        )}
                        {citizen.has_id_card && (
                          <IconButton
                            onClick={() => navigate(`/idcards?citizen=${citizen.id}`)}
                            size="small"
                            sx={{
                              bgcolor: 'info.main',
                              color: 'white',
                              '&:hover': { bgcolor: 'info.dark' }
                            }}
                          >
                            <BadgeIcon fontSize="small" />
                          </IconButton>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          borderTop: '1px solid',
          borderColor: 'divider'
        }}>
          <Typography variant="body2" color="text.secondary">
            {totalCount} citizens found
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Rows per page:
            </Typography>
            <Select
              value={rowsPerPage}
              onChange={handleChangeRowsPerPage}
              size="small"
              sx={{ minWidth: 80 }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
            <Typography variant="body2" color="text.secondary">
              {page * rowsPerPage + 1}–{Math.min((page + 1) * rowsPerPage, totalCount)} of {totalCount}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton
                onClick={(e) => handleChangePage(e, page - 1)}
                disabled={page === 0}
                size="small"
              >
                <ChevronLeftIcon />
              </IconButton>
              <IconButton
                onClick={(e) => handleChangePage(e, page + 1)}
                disabled={page >= Math.ceil(totalCount / rowsPerPage) - 1}
                size="small"
              >
                <ChevronRightIcon />
              </IconButton>
            </Box>
          </Box>
        </Box>
      </Paper>

    </Box>
  );
};

export default CitizensList;
