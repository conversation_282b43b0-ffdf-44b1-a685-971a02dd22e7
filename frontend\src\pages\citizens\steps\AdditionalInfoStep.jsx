import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from '@mui/material';
import { Info as InfoIcon } from '@mui/icons-material';
import EthiopianCalendarWidget from '../../../components/common/EthiopianCalendarWidget';
import localizationService from '../../../services/localizationService';

const AdditionalInfoStep = ({ formik, loading }) => {
  return (
      <Card>
        {/* Header with blue background */}
        <Box sx={{
          bgcolor: '#2196f3',
          color: 'white',
          p: 2,
          display: 'flex',
          alignItems: 'center'
        }}>
          <InfoIcon sx={{ mr: 1 }} />
          <Typography variant="h6" component="h2">
            {localizationService.t('additional_information')}
          </Typography>
        </Box>

        <CardContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {localizationService.t('additional_information_description')}
          </Typography>

          <Grid container spacing={3}>
            {/* Employment Information */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.employment && Boolean(formik.errors.employment)}>
                <InputLabel id="employment-label">{localizationService.t('employment_status')}</InputLabel>
                <Select
                  labelId="employment-label"
                  id="employment"
                  name="employment"
                  value={formik.values.employment}
                  label={localizationService.t('employment_status')}
                  onChange={formik.handleChange}
                >
                  <MenuItem value="employed">{localizationService.t('employed')}</MenuItem>
                  <MenuItem value="unemployed">{localizationService.t('unemployed')}</MenuItem>
                  <MenuItem value="self_employed">{localizationService.t('self_employed')}</MenuItem>
                  <MenuItem value="student">{localizationService.t('student')}</MenuItem>
                  <MenuItem value="retired">{localizationService.t('retired')}</MenuItem>
                </Select>
                {formik.touched.employment && formik.errors.employment && (
                  <FormHelperText>{formik.errors.employment}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="organization_name"
                name="organization_name"
                label={localizationService.t('organization_name')}
                value={formik.values.organization_name}
                onChange={formik.handleChange}
                error={formik.touched.organization_name && Boolean(formik.errors.organization_name)}
                helperText={formik.touched.organization_name && formik.errors.organization_name}
              />
            </Grid>

            {/* Additional Fields */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.employee_type && Boolean(formik.errors.employee_type)}>
                <InputLabel id="employee-type-label">{localizationService.t('employee_type')}</InputLabel>
                <Select
                  labelId="employee-type-label"
                  id="employee_type"
                  name="employee_type"
                  value={formik.values.employee_type}
                  label={localizationService.t('employee_type')}
                  onChange={formik.handleChange}
                >
                  <MenuItem value="permanent">{localizationService.t('permanent')}</MenuItem>
                  <MenuItem value="contract">{localizationService.t('contract')}</MenuItem>
                  <MenuItem value="temporary">{localizationService.t('temporary')}</MenuItem>
                  <MenuItem value="part_time">{localizationService.t('part_time')}</MenuItem>
                  <MenuItem value="freelance">{localizationService.t('freelance')}</MenuItem>
                </Select>
                {formik.touched.employee_type && formik.errors.employee_type && (
                  <FormHelperText>{formik.errors.employee_type}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="region"
                name="region"
                label={localizationService.t('region')}
                value={formik.values.region}
                onChange={formik.handleChange}
                error={formik.touched.region && Boolean(formik.errors.region)}
                helperText={formik.touched.region && formik.errors.region}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
  );
};

export default AdditionalInfoStep;
