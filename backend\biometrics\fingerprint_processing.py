"""
Fingerprint processing utilities for quality validation, 
duplicate detection, and template management.
"""

import json
import base64
import logging
import os
import subprocess
import tempfile
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from django.db import connection
from django_tenants.utils import schema_context, get_tenant_model

logger = logging.getLogger(__name__)


class FingerprintProcessor:
    """
    Handles fingerprint template processing, validation, and duplicate detection.
    """
    
    def __init__(self):
        self.quality_threshold = 75
        self.match_threshold = 40  # FMatcher threshold for duplicate detection
        self.min_minutiae = 30
        self.debug_mode = False  # Disabled - using real duplicate detection
        # FMatcher will run on the same server as Django
        self.fmatcher_jar_path = os.path.join(os.path.dirname(__file__), '..', '..', 'fpmatcher', 'FMatcher.jar')
        self.fmatcher_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'fpmatcher')
    
    def validate_template_quality(self, template_data: str) -> Dict:
        """
        Validate the quality of a fingerprint template.
        
        Args:
            template_data (str): Base64 encoded fingerprint template
            
        Returns:
            Dict: Quality validation result
        """
        try:
            # Decode and parse template
            decoded_data = base64.b64decode(template_data)
            template = json.loads(decoded_data.decode())
            
            quality_score = template.get('quality_metrics', {}).get('clarity', 0)
            minutiae_count = len(template.get('minutiae', []))
            
            # Validate quality criteria
            is_valid = True
            issues = []
            
            if quality_score < self.quality_threshold:
                is_valid = False
                issues.append(f"Quality score {quality_score}% below threshold {self.quality_threshold}%")
            
            if minutiae_count < self.min_minutiae:
                is_valid = False
                issues.append(f"Minutiae count {minutiae_count} below minimum {self.min_minutiae}")
            
            return {
                'is_valid': is_valid,
                'quality_score': quality_score,
                'minutiae_count': minutiae_count,
                'issues': issues,
                'validation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error validating template quality: {e}")
            return {
                'is_valid': False,
                'quality_score': 0,
                'minutiae_count': 0,
                'issues': [f"Template validation error: {str(e)}"],
                'validation_time': datetime.now().isoformat()
            }
    
    def check_duplicates(self, fingerprint_data: Dict) -> Dict:
        """
        Check for duplicate fingerprints across all tenants.

        Args:
            fingerprint_data (Dict): Dictionary containing fingerprint templates

        Returns:
            Dict: Duplicate check results
        """
        try:
            logger.info("🔍 DUPLICATE CHECK STARTED")
            logger.info(f"🔍 Input data keys: {list(fingerprint_data.keys())}")

            matches = []
            left_thumb = fingerprint_data.get('left_thumb_fingerprint')
            right_thumb = fingerprint_data.get('right_thumb_fingerprint')

            logger.info(f"🔍 Left thumb data: {len(left_thumb) if left_thumb else 0} chars")
            logger.info(f"🔍 Right thumb data: {len(right_thumb) if right_thumb else 0} chars")

            if not left_thumb and not right_thumb:
                logger.info("❌ No fingerprint data to check")
                return {
                    'has_duplicates': False,
                    'matches': [],
                    'check_time': datetime.now().isoformat()
                }

            # Use FMatcher service for accurate duplicate detection
            return self._check_duplicates_with_fmatcher(left_thumb, right_thumb)

        except Exception as e:
            logger.error(f"Error checking duplicates: {e}")
            return {
                'has_duplicates': False,
                'matches': [],
                'error': str(e),
                'check_time': datetime.now().isoformat()
            }
    
    def _check_tenant_duplicates(self, left_thumb: str, right_thumb: str, tenant) -> List[Dict]:
        """
        Check for duplicates within a specific tenant.

        Args:
            left_thumb (str): Left thumb template
            right_thumb (str): Right thumb template
            tenant: Tenant object

        Returns:
            List[Dict]: List of matches found
        """
        matches = []

        try:
            # Import here to avoid circular imports
            from tenants.models.citizen import Biometric

            # Get all biometric records in this tenant
            biometrics = Biometric.objects.select_related('citizen').all()
            logger.info(f"🔍 Tenant {tenant.name}: Found {biometrics.count()} biometric records to check")

            for i, biometric in enumerate(biometrics):
                logger.info(f"🔍 Checking biometric {i+1}/{biometrics.count()}: Citizen {biometric.citizen.first_name} {biometric.citizen.last_name}")

                match_score = 0.0
                match_details = []

                # Check left thumb match
                if left_thumb and biometric.left_thumb_fingerprint:
                    logger.info(f"🔍 Comparing left thumbs...")
                    logger.info(f"   Input left thumb: {len(left_thumb)} chars")
                    logger.info(f"   Stored left thumb: {len(biometric.left_thumb_fingerprint)} chars")

                    left_match, left_confidence = self._compare_templates(
                        left_thumb, biometric.left_thumb_fingerprint
                    )
                    logger.info(f"   Left thumb result: match={left_match}, confidence={left_confidence:.3f}")

                    if left_match:
                        match_score += 0.5
                        match_details.append(f"Left thumb match (confidence: {left_confidence:.2f})")
                        logger.info(f"✅ LEFT THUMB MATCH DETECTED!")

                # Check right thumb match
                if right_thumb and biometric.right_thumb_fingerprint:
                    logger.info(f"🔍 Comparing right thumbs...")
                    logger.info(f"   Input right thumb: {len(right_thumb)} chars")
                    logger.info(f"   Stored right thumb: {len(biometric.right_thumb_fingerprint)} chars")

                    right_match, right_confidence = self._compare_templates(
                        right_thumb, biometric.right_thumb_fingerprint
                    )
                    logger.info(f"   Right thumb result: match={right_match}, confidence={right_confidence:.3f}")

                    if right_match:
                        match_score += 0.5
                        match_details.append(f"Right thumb match (confidence: {right_confidence:.2f})")
                        logger.info(f"✅ RIGHT THUMB MATCH DETECTED!")

                # If we have a significant match, record it
                if match_score >= 0.5:  # At least one thumb matches
                    logger.info(f"🚨 DUPLICATE FOUND: {biometric.citizen.first_name} {biometric.citizen.last_name} (score: {match_score})")
                    matches.append({
                        'tenant_id': tenant.id,
                        'tenant_name': tenant.name,
                        'citizen_id': biometric.citizen.id,
                        'citizen_name': f"{biometric.citizen.first_name} {biometric.citizen.last_name}",
                        'citizen_digital_id': biometric.citizen.digital_id,
                        'match_score': match_score,
                        'match_details': match_details,
                        'biometric_id': biometric.id
                    })
                else:
                    logger.info(f"❌ No match for {biometric.citizen.first_name} {biometric.citizen.last_name} (score: {match_score})")

            logger.info(f"🔍 Tenant {tenant.name} check complete: {len(matches)} matches found")

        except Exception as e:
            logger.error(f"Error checking tenant {tenant.name} for duplicates: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")

        return matches
    
    def _compare_templates(self, template1: str, template2: str) -> Tuple[bool, float]:
        """
        Compare two fingerprint templates.

        Args:
            template1 (str): First template
            template2 (str): Second template

        Returns:
            Tuple[bool, float]: (is_match, confidence_score)
        """
        try:
            logger.info(f"🔍 Comparing fingerprint templates...")
            logger.info(f"   Template 1 length: {len(template1) if template1 else 0}")
            logger.info(f"   Template 2 length: {len(template2) if template2 else 0}")

            # Check for exact match first
            if template1 == template2:
                logger.info("✅ Exact template match found!")
                return True, 1.0

            # For real biometric data, we need to compare the actual fingerprint characteristics
            # Since we're using real Futronic device data, let's implement a more realistic comparison

            try:
                # Try to decode and compare templates
                decoded1 = json.loads(base64.b64decode(template1).decode())
                decoded2 = json.loads(base64.b64decode(template2).decode())

                logger.info(f"🔍 Decoded templates successfully")
                logger.info(f"   Template 1 version: {decoded1.get('version', 'Unknown')}")
                logger.info(f"   Template 2 version: {decoded2.get('version', 'Unknown')}")
                logger.info(f"   Template 1 device: {decoded1.get('device_info', {}).get('model', 'Unknown')}")
                logger.info(f"   Template 2 device: {decoded2.get('device_info', {}).get('model', 'Unknown')}")

                # Check if we have actual minutiae points (version 2.0+)
                has_minutiae1 = 'minutiae_points' in decoded1 and decoded1['minutiae_points']
                has_minutiae2 = 'minutiae_points' in decoded2 and decoded2['minutiae_points']

                logger.info(f"   Template 1 has minutiae points: {len(decoded1.get('minutiae_points', []))}")
                logger.info(f"   Template 2 has minutiae points: {len(decoded2.get('minutiae_points', []))}")

                similarity_score = 0.0

                # If both templates have minutiae points, compare them using proper biometric matching
                if has_minutiae1 and has_minutiae2:
                    logger.info("🔍 Comparing minutiae points for biometric matching...")

                    # Get the minutiae points
                    minutiae1 = decoded1['minutiae_points']
                    minutiae2 = decoded2['minutiae_points']

                    # Use proper minutiae-based matching
                    minutiae_similarity = self._calculate_minutiae_similarity_advanced(minutiae1, minutiae2)
                    similarity_score = minutiae_similarity

                    logger.info(f"🔍 Minutiae comparison:")
                    logger.info(f"   Template 1 minutiae: {len(minutiae1)} points")
                    logger.info(f"   Template 2 minutiae: {len(minutiae2)} points")
                    logger.info(f"   Minutiae similarity: {minutiae_similarity:.3f}")

                # Fallback: Check for fingerprint image data if no minutiae
                elif 'fingerprint_image' in decoded1 and 'fingerprint_image' in decoded2:
                    logger.info("🔍 Comparing fingerprint images (fallback)...")

                    image1_b64 = decoded1['fingerprint_image']
                    image2_b64 = decoded2['fingerprint_image']

                    if image1_b64 == image2_b64:
                        similarity_score = 1.0
                        logger.info("🚨 IDENTICAL FINGERPRINT IMAGES DETECTED!")
                    else:
                        # Basic image comparison
                        try:
                            image1_data = base64.b64decode(image1_b64)
                            image2_data = base64.b64decode(image2_b64)

                            size_diff = abs(len(image1_data) - len(image2_data))
                            max_size = max(len(image1_data), len(image2_data))
                            size_similarity = 1.0 - (size_diff / max_size) if max_size > 0 else 0.0

                            sample_size = min(1000, len(image1_data), len(image2_data))
                            if sample_size > 0:
                                sample1 = image1_data[:sample_size]
                                sample2 = image2_data[:sample_size]
                                matching_bytes = sum(1 for a, b in zip(sample1, sample2) if a == b)
                                byte_similarity = matching_bytes / sample_size
                                similarity_score = (size_similarity * 0.3) + (byte_similarity * 0.7)

                        except Exception as img_error:
                            logger.warning(f"Error comparing fingerprint images: {img_error}")
                            similarity_score = 0.1


                # 1. Check if both are from the same device type
                device1 = decoded1.get('device_info', {}).get('model', '')
                device2 = decoded2.get('device_info', {}).get('model', '')
                if device1 == device2 and device1 == 'Futronic FS88H':
                    similarity_score += 0.05  # Small bonus for same device
                    logger.info("✅ Same device type detected")

                # 2. Compare thumb types (if specified)
                thumb1 = decoded1.get('thumb_type', '')
                thumb2 = decoded2.get('thumb_type', '')
                if thumb1 and thumb2 and thumb1 == thumb2:
                    similarity_score += 0.05  # Small bonus for same thumb
                    logger.info(f"✅ Same thumb type: {thumb1}")

                # Determine if it's a match
                is_match = similarity_score >= self.match_threshold

                logger.info(f"🔍 Biometric comparison result:")
                logger.info(f"   Similarity score: {similarity_score:.3f}")
                logger.info(f"   Match threshold: {self.match_threshold}")
                logger.info(f"   Is match: {is_match}")
                comparison_type = 'Minutiae-based' if has_minutiae1 and has_minutiae2 else 'Image-based' if 'fingerprint_image' in decoded1 else 'Metadata only'
                logger.info(f"   Comparison type: {comparison_type}")

                return is_match, similarity_score

            except Exception as decode_error:
                logger.warning(f"Could not decode templates as JSON: {decode_error}")

                # Fallback: Use string similarity for basic comparison
                # This handles cases where templates might be similar but not identical
                if len(template1) > 100 and len(template2) > 100:
                    # For longer templates, check if they share significant portions
                    common_chars = sum(1 for a, b in zip(template1[:100], template2[:100]) if a == b)
                    similarity = common_chars / 100.0

                    # Also check overall length similarity
                    length_similarity = min(len(template1), len(template2)) / max(len(template1), len(template2))

                    # Combine similarities
                    overall_similarity = (similarity * 0.7) + (length_similarity * 0.3)

                    is_match = overall_similarity >= self.match_threshold

                    logger.info(f"🔍 Fallback string comparison:")
                    logger.info(f"   Character similarity: {similarity:.2f}")
                    logger.info(f"   Length similarity: {length_similarity:.2f}")
                    logger.info(f"   Overall similarity: {overall_similarity:.2f}")
                    logger.info(f"   Is match: {is_match}")

                    return is_match, overall_similarity
                else:
                    # For very short templates or other cases, use a simple hash-based comparison
                    # This is a fallback for testing - if templates are identical, they should match
                    if template1 and template2:
                        # Check if templates are very similar (for testing purposes)
                        if len(template1) == len(template2):
                            # If same length, check character similarity
                            similarity = sum(1 for a, b in zip(template1, template2) if a == b) / len(template1)
                            is_match = similarity >= 0.9  # 90% character similarity
                            logger.info(f"🔍 Short template comparison: {similarity:.2f} similarity")
                            return is_match, similarity

                    return False, 0.0

        except Exception as e:
            logger.error(f"Error comparing templates: {e}")
            return False, 0.0
    
    def _calculate_minutiae_similarity(self, minutiae1: List, minutiae2: List) -> float:
        """
        Calculate similarity between two sets of minutiae points.

        This is a simplified implementation for development.
        Production systems would use proper biometric matching algorithms.
        """
        if not minutiae1 or not minutiae2:
            return 0.0

        # Simple similarity based on minutiae count
        count_similarity = min(len(minutiae1), len(minutiae2)) / max(len(minutiae1), len(minutiae2))

        # Add some randomness for demo purposes
        import random
        position_similarity = random.uniform(0.6, 0.9)

        # Combine similarities
        overall_similarity = (count_similarity * 0.3) + (position_similarity * 0.7)

        return min(overall_similarity, 1.0)

    def _calculate_minutiae_similarity_advanced(self, minutiae1: List, minutiae2: List) -> float:
        """
        Advanced minutiae-based fingerprint matching algorithm.

        This implements a proper biometric matching algorithm that compares:
        1. Minutiae point coordinates (X, Y positions)
        2. Minutiae angles and types
        3. Spatial relationships between points
        4. Quality scores for weighting

        Args:
            minutiae1 (List): First set of minutiae points
            minutiae2 (List): Second set of minutiae points

        Returns:
            float: Similarity score between 0.0 and 1.0
        """
        if not minutiae1 or not minutiae2:
            logger.warning("One or both minutiae sets are empty")
            return 0.0

        logger.info(f"🔍 Advanced minutiae matching:")
        logger.info(f"   Set 1: {len(minutiae1)} points")
        logger.info(f"   Set 2: {len(minutiae2)} points")

        # Parameters for matching (more lenient for real-world scenarios)
        COORDINATE_TOLERANCE = 10  # pixels (increased tolerance)
        ANGLE_TOLERANCE = 30       # degrees (increased tolerance)
        MIN_MATCHING_POINTS = 8    # minimum points for a match (reduced)
        MATCH_THRESHOLD = 0.6      # threshold for considering a point matched (reduced)

        matched_points = 0
        total_quality_score = 0.0

        # For each minutiae point in set 1, find the best match in set 2
        for m1 in minutiae1:
            best_match_score = 0.0

            for m2 in minutiae2:
                # Skip if different types (ridge_ending vs bifurcation)
                if m1.get('type') != m2.get('type'):
                    continue

                # Calculate coordinate distance
                x_diff = abs(m1.get('x', 0) - m2.get('x', 0))
                y_diff = abs(m1.get('y', 0) - m2.get('y', 0))
                coordinate_distance = (x_diff ** 2 + y_diff ** 2) ** 0.5

                # Check if within coordinate tolerance
                if coordinate_distance > COORDINATE_TOLERANCE:
                    continue

                # Calculate angle difference (handle circular nature of angles)
                angle1 = m1.get('angle', 0)
                angle2 = m2.get('angle', 0)
                angle_diff = abs(angle1 - angle2)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff

                # Check if within angle tolerance
                if angle_diff > ANGLE_TOLERANCE:
                    continue

                # Calculate match score for this pair
                coord_score = max(0, 1.0 - (coordinate_distance / COORDINATE_TOLERANCE))
                angle_score = max(0, 1.0 - (angle_diff / ANGLE_TOLERANCE))

                # Weight by quality scores
                quality1 = m1.get('quality', 0.8)
                quality2 = m2.get('quality', 0.8)
                quality_factor = (quality1 + quality2) / 2.0

                # Combined match score
                match_score = (coord_score * 0.4 + angle_score * 0.6) * quality_factor

                if match_score > best_match_score:
                    best_match_score = match_score

            # If we found a good match for this minutiae point
            if best_match_score > MATCH_THRESHOLD:  # threshold for considering a point matched
                matched_points += 1
                total_quality_score += best_match_score

        # Calculate overall similarity
        if matched_points == 0:
            similarity = 0.0
        else:
            # Base similarity on ratio of matched points to smaller set
            smaller_set_size = min(len(minutiae1), len(minutiae2))
            match_ratio = matched_points / smaller_set_size

            # Average quality of matches
            avg_match_quality = total_quality_score / matched_points

            # Bonus for having enough matching points
            sufficient_points_bonus = 1.0 if matched_points >= MIN_MATCHING_POINTS else 0.9

            # Final similarity score (more generous calculation)
            similarity = match_ratio * avg_match_quality * sufficient_points_bonus

        logger.info(f"   Matched points: {matched_points}")
        if matched_points > 0:
            logger.info(f"   Match ratio: {match_ratio:.3f}")
            logger.info(f"   Average match quality: {avg_match_quality:.3f}")
        logger.info(f"   Final similarity: {similarity:.3f}")

        return min(similarity, 1.0)
    
    def get_biometric_statistics(self) -> Dict:
        """
        Get biometric statistics for the current tenant.

        Returns:
            Dict: Statistics about biometric data
        """
        try:
            # Import here to avoid circular imports
            from tenants.models.citizen import Biometric, Citizen

            # Count total citizens and biometric records
            total_citizens = Citizen.objects.count()
            total_biometrics = Biometric.objects.count()
            
            # Count by fingerprint availability
            try:
                left_thumb_count = Biometric.objects.exclude(
                    left_thumb_fingerprint__isnull=True
                ).exclude(left_thumb_fingerprint='').count()

                right_thumb_count = Biometric.objects.exclude(
                    right_thumb_fingerprint__isnull=True
                ).exclude(right_thumb_fingerprint='').count()

                both_thumbs_count = Biometric.objects.exclude(
                    left_thumb_fingerprint__isnull=True
                ).exclude(left_thumb_fingerprint='').exclude(
                    right_thumb_fingerprint__isnull=True
                ).exclude(right_thumb_fingerprint='').count()
            except Exception as query_error:
                logger.warning(f"Error querying biometric data: {query_error}")
                # Fallback to zero counts
                left_thumb_count = 0
                right_thumb_count = 0
                both_thumbs_count = 0
            
            # Calculate percentages
            biometric_coverage = (total_biometrics / total_citizens * 100) if total_citizens > 0 else 0
            left_thumb_coverage = (left_thumb_count / total_citizens * 100) if total_citizens > 0 else 0
            right_thumb_coverage = (right_thumb_count / total_citizens * 100) if total_citizens > 0 else 0
            complete_coverage = (both_thumbs_count / total_citizens * 100) if total_citizens > 0 else 0
            
            return {
                'total_citizens': total_citizens,
                'total_biometric_records': total_biometrics,
                'biometric_coverage_percent': round(biometric_coverage, 2),
                'fingerprint_stats': {
                    'left_thumb_count': left_thumb_count,
                    'right_thumb_count': right_thumb_count,
                    'both_thumbs_count': both_thumbs_count,
                    'left_thumb_coverage_percent': round(left_thumb_coverage, 2),
                    'right_thumb_coverage_percent': round(right_thumb_coverage, 2),
                    'complete_coverage_percent': round(complete_coverage, 2)
                },
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting biometric statistics: {e}")
            return {
                'error': str(e),
                'generated_at': datetime.now().isoformat()
            }
    
    def _check_duplicates_with_fmatcher(self, left_thumb, right_thumb):
        """Use FMatcher service to check for duplicates across all tenants"""
        try:
            logger.info("🔍 Using FMatcher service for duplicate detection")
            
            # Get all stored templates from all tenants
            stored_templates = self._get_all_stored_templates()
            
            if not stored_templates:
                logger.info("No stored templates found - no duplicates possible")
                return {
                    'has_duplicates': False,
                    'matches': [],
                    'total_matches': 0,
                    'check_time': datetime.now().isoformat(),
                    'matcher': 'FMatcher'
                }
            
            logger.info(f"🔍 Checking against {len(stored_templates)} stored templates")
            
            matches = []
            
            # Check left thumb if available
            if left_thumb:
                left_matches = self._check_template_against_stored(left_thumb, stored_templates, 'left')
                matches.extend(left_matches)
            
            # Check right thumb if available
            if right_thumb:
                right_matches = self._check_template_against_stored(right_thumb, stored_templates, 'right')
                matches.extend(right_matches)
            
            # Remove duplicates and sort by score
            unique_matches = []
            seen_citizens = set()
            
            for match in sorted(matches, key=lambda x: x['match_score'], reverse=True):
                citizen_key = f"{match['tenant_id']}_{match['citizen_id']}"
                if citizen_key not in seen_citizens:
                    unique_matches.append(match)
                    seen_citizens.add(citizen_key)
            
            has_duplicates = len(unique_matches) > 0
            
            if has_duplicates:
                logger.warning(f"🚨 FRAUD ALERT: Found {len(unique_matches)} duplicate fingerprint(s)")
                for match in unique_matches[:3]:  # Log first 3
                    logger.warning(f"   📍 Match: {match['citizen_name']} in {match['tenant_name']} (Score: {match['match_score']:.1f})")
            else:
                logger.info("✅ No duplicates found - fingerprints are unique")
            
            return {
                'has_duplicates': has_duplicates,
                'matches': unique_matches,
                'total_matches': len(unique_matches),
                'check_time': datetime.now().isoformat(),
                'matcher': 'FMatcher',
                'templates_checked': len(stored_templates)
            }
            
        except Exception as e:
            logger.error(f"Error in FMatcher duplicate check: {e}")
            return {
                'has_duplicates': False,
                'matches': [],
                'error': str(e),
                'check_time': datetime.now().isoformat(),
                'matcher': 'FMatcher (error)'
            }
    
    def _get_all_stored_templates(self):
        """Get all fingerprint templates from all tenants"""
        stored_templates = []
        
        try:
            # Get all kebele tenants
            Tenant = get_tenant_model()
            tenants = Tenant.objects.filter(type='kebele')
            
            for tenant in tenants:
                try:
                    with schema_context(tenant.schema_name):
                        # Import inside Django apps to avoid circular imports in Docker
                        from django.apps import apps
                        BiometricRecord = apps.get_model('biometrics', 'BiometricRecord')
                        
                        biometric_records = BiometricRecord.objects.select_related('citizen').all()
                        
                        for record in biometric_records:
                            if record.left_thumb_fingerprint:
                                stored_templates.append({
                                    'template_data': record.left_thumb_fingerprint,
                                    'thumb_type': 'left',
                                    'tenant_id': tenant.id,
                                    'tenant_name': tenant.name,
                                    'citizen_id': record.citizen.id,
                                    'citizen_name': f"{record.citizen.first_name} {record.citizen.last_name}",
                                    'citizen_digital_id': record.citizen.digital_id,
                                    'biometric_id': record.id
                                })
                            
                            if record.right_thumb_fingerprint:
                                stored_templates.append({
                                    'template_data': record.right_thumb_fingerprint,
                                    'thumb_type': 'right',
                                    'tenant_id': tenant.id,
                                    'tenant_name': tenant.name,
                                    'citizen_id': record.citizen.id,
                                    'citizen_name': f"{record.citizen.first_name} {record.citizen.last_name}",
                                    'citizen_digital_id': record.citizen.digital_id,
                                    'biometric_id': record.id
                                })
                        
                        logger.info(f"📊 Found {len([t for t in stored_templates if t['tenant_name'] == tenant.name])} templates in {tenant.name}")
                        
                except Exception as e:
                    logger.error(f"Error getting templates from {tenant.name}: {e}")
                    continue
            
            return stored_templates
            
        except Exception as e:
            logger.error(f"Error getting stored templates: {e}")
            return []
    
    def _check_template_against_stored(self, new_template, stored_templates, thumb_type):
        """Check one template against all stored templates using local FMatcher"""
        matches = []
        
        try:
            # Extract just the ANSI/ISO template data from the JSON
            template_json = json.loads(new_template)
            ansi_template = template_json.get('ansi_iso_template')
            
            if not ansi_template:
                logger.warning(f"No ANSI/ISO template found in {thumb_type} thumb data")
                return matches
            
            # Decode the new template
            new_template_bytes = base64.b64decode(ansi_template)
            
            # Check against each stored template individually
            for i, stored in enumerate(stored_templates):
                try:
                    stored_json = json.loads(stored['template_data'])
                    stored_ansi = stored_json.get('ansi_iso_template')
                    
                    if not stored_ansi:
                        continue
                    
                    stored_template_bytes = base64.b64decode(stored_ansi)
                    
                    # Use FMatcher to compare templates
                    score = self._compare_templates_with_fmatcher(new_template_bytes, stored_template_bytes)
                    
                    if score is not None and score > self.match_threshold:
                        # This is a duplicate!
                        matches.append({
                            'tenant_id': stored['tenant_id'],
                            'tenant_name': stored['tenant_name'],
                            'citizen_id': stored['citizen_id'],
                            'citizen_name': stored['citizen_name'],
                            'citizen_digital_id': stored['citizen_digital_id'],
                            'biometric_id': stored['biometric_id'],
                            'match_score': score,
                            'confidence': min(score / 100.0, 1.0),
                            'match_quality': 'High' if score > 100 else 'Medium',
                            'matched_thumb': stored['thumb_type'],
                            'new_thumb': thumb_type,
                            'match_details': [f"FMatcher score: {score:.1f}"]
                        })
                        
                        logger.warning(f"🚨 DUPLICATE FOUND: {stored['citizen_name']} in {stored['tenant_name']} (Score: {score:.1f})")
                    
                except Exception as template_error:
                    logger.error(f"Error comparing template {i}: {template_error}")
                    continue
            
            return matches
                
        except Exception as e:
            logger.error(f"Error in template matching: {e}")
            return matches
    
    def _compare_templates_with_fmatcher(self, template1_bytes, template2_bytes):
        """Compare two templates using FMatcher.jar"""
        try:
            if not os.path.exists(self.fmatcher_jar_path):
                logger.error(f"FMatcher.jar not found at {self.fmatcher_jar_path}")
                return None
            
            # Create temporary files for the templates
            with tempfile.NamedTemporaryFile(suffix='.iso', delete=False) as temp1:
                temp1.write(template1_bytes)
                temp1_path = temp1.name
            
            with tempfile.NamedTemporaryFile(suffix='.iso', delete=False) as temp2:
                temp2.write(template2_bytes)
                temp2_path = temp2.name
            
            try:
                # Run FMatcher.jar
                result = subprocess.run(
                    ['java', '-jar', os.path.basename(self.fmatcher_jar_path), temp1_path, temp2_path],
                    cwd=self.fmatcher_dir,
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    score = float(result.stdout.strip())
                    return score
                else:
                    logger.error(f"FMatcher failed: {result.stderr}")
                    return None
                    
            finally:
                # Clean up temporary files
                try:
                    os.unlink(temp1_path)
                    os.unlink(temp2_path)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"Error running FMatcher: {e}")
            return None
