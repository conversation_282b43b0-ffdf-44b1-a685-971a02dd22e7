import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  Chip,
  Divider
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
  ResponsiveContainer
} from 'recharts';
import {
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Badge as BadgeIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  SwapHoriz as MigrationIcon,
  Accessibility as DisabilityIcon,
  Favorite as HeartIcon,
  LocationOn as LocationIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';
import '../../styles/dashboard.css';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';
import { useLocalization } from '../../contexts/LocalizationContext';

// Chart colors - diverse palette for better data visualization
const COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
  '#8B5CF6', '#06B6D4', '#84CC16', '#F97316',
  '#EC4899', '#6366F1', '#14B8A6', '#F43F5E'
];

// GRADIENTS will be created dynamically using theme colors

const KebeleDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { tenantColors } = useAppTheme();
  const { t, currentLanguage } = useLocalization();
  
  // Debug translations
  console.log(`🏠 KebeleDashboard rendering with language: ${currentLanguage}`);
  console.log(`🔤 KebeleDashboard test translations:`, {
    dashboard: t('dashboard', 'DEFAULT_DASHBOARD'),
    citizens: t('citizens', 'DEFAULT_CITIZENS'),
    total_citizens: t('total_citizens', 'DEFAULT_TOTAL_CITIZENS'),
    pending_approvals: t('pending_approvals', 'DEFAULT_PENDING_APPROVALS')
  });

  // Dynamic color definitions based on theme
  const PRIMARY_COLOR = tenantColors.primary;
  const SECONDARY_COLOR = tenantColors.secondary;

  const GRADIENTS = {
    primary: `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, ${SECONDARY_COLOR} 100%)`,
    success: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
    warning: `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, #f57c00 100%)`,
    error: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    info: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      console.log('🚨 KebeleDashboard: Starting to fetch dashboard data...');
      setLoading(true);

      // Check if we have a token
      const token = localStorage.getItem('accessToken');
      console.log('🚨 KebeleDashboard: Token exists:', !!token);

      console.log('🚨 KebeleDashboard: Calling dashboard endpoint...');
      const response = await axios.get('/api/kebele-dashboard/');
      console.log('🚨 KebeleDashboard: Response received:', response.data);
      setDashboardData(response.data.data);
      setError(null);
    } catch (error) {
      console.error('🚨 KebeleDashboard: Error fetching dashboard data:', error);
      console.error('🚨 KebeleDashboard: Error response:', error.response);
      setError(`Failed to load dashboard data: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">{t('loading', 'Loading dashboard data...')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="m-4 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <ErrorIcon className="text-red-500 mr-2" />
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="m-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center">
          <BadgeIcon className="text-blue-500 mr-2" />
          <p className="text-blue-700">{t('no_data_available', 'No dashboard data available')}</p>
        </div>
      </div>
    );
  }

  // Enhanced Big Number Card Component with Tailwind CSS and Animations
  const BigNumberCard = ({ title, value, icon, color = 'primary', trend = null }) => {
    const colorClasses = {
      primary: `bg-gradient-to-br from-[${PRIMARY_COLOR}] to-[${SECONDARY_COLOR}] hover:from-[#e65100] hover:to-[#d84315]`,
      success: 'bg-gradient-to-br from-green-500 to-green-700 hover:from-green-600 hover:to-green-800',
      warning: `bg-gradient-to-br from-[${PRIMARY_COLOR}] to-yellow-600 hover:from-[#e65100] hover:to-yellow-700`,
      error: 'bg-gradient-to-br from-red-500 to-red-700 hover:from-red-600 hover:to-red-800',
    };

    return (
      <div className={`
        relative overflow-hidden rounded-2xl p-6 text-white shadow-lg transition-all duration-500 ease-in-out
        transform hover:-translate-y-2 hover:shadow-2xl cursor-pointer group animate-scale-in
        ${colorClasses[color] || colorClasses.primary}
      `}
      style={{
        background: color === 'primary' ? `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, ${SECONDARY_COLOR} 100%)` :
                   color === 'warning' ? `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, #f57c00 100%)` : undefined
      }}>
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 bg-white opacity-5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px] animate-pulse"></div>

        {/* Floating Animation Elements */}
        <div className="absolute top-2 right-2 w-4 h-4 bg-white/20 rounded-full animate-bounce" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-4 left-4 w-2 h-2 bg-white/30 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>

        <div className="relative flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-white/90 mb-2 tracking-wide animate-fade-in">
              {title}
            </h3>
            <p className="text-3xl font-bold text-white mb-1 group-hover:scale-110 transition-transform duration-300 animate-slide-up">
              {value?.toLocaleString() || 0}
            </p>
            {trend && (
              <div className="flex items-center mt-2 text-white/80 animate-fade-in" style={{ animationDelay: '0.3s' }}>
                <TrendingUpIcon className="w-4 h-4 mr-1 animate-bounce" />
                <span className="text-sm">{trend}</span>
              </div>
            )}
          </div>
          <div className="text-white/70 text-5xl group-hover:text-white/90 group-hover:rotate-12 transition-all duration-300 animate-scale-in" style={{ animationDelay: '0.2s' }}>
            {icon}
          </div>
        </div>

        {/* Enhanced Hover Effect */}
        <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-15 transition-opacity duration-500"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
      </div>
    );
  };

  // Enhanced Chart Card Component with Tailwind CSS and Animations
  const ChartCard = ({ title, children, height = 400, icon = null, gradient = null }) => (
    <div className="bg-white rounded-2xl shadow-soft hover:shadow-large transition-all duration-700 ease-in-out transform hover:-translate-y-3 hover:scale-[1.02] border border-gray-100 overflow-hidden group animate-fade-in">
      {/* Header with gradient background */}
      <div
        className={`p-6 pb-4 border-b border-gray-100 relative overflow-hidden`}
        style={{
          background: gradient ? `linear-gradient(to right, ${PRIMARY_COLOR}10, ${SECONDARY_COLOR}10)` : '#f9fafb'
        }}
      >
        {/* Animated background elements */}
        <div
          className="absolute top-0 right-0 w-20 h-20 rounded-full -translate-y-10 translate-x-10 group-hover:scale-150 transition-transform duration-1000"
          style={{
            background: `linear-gradient(to bottom right, ${PRIMARY_COLOR}20, ${SECONDARY_COLOR}20)`
          }}
        ></div>

        <div className="flex items-center justify-between relative z-10">
          <div className="flex items-center">
            {icon && (
              <div className="mr-3 p-2 rounded-lg group-hover:scale-110 transition-all duration-300 animate-bounce"
                   style={{
                     backgroundColor: `${PRIMARY_COLOR}20`,
                     color: PRIMARY_COLOR,
                     animationDelay: '0.2s'
                   }}>
                {icon}
              </div>
            )}
            <h3 className="text-lg font-bold mb-0 transition-colors duration-300 animate-slide-up"
                style={{ color: PRIMARY_COLOR }}>
              {title}
            </h3>
          </div>
          <div className="flex space-x-1">
            <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: PRIMARY_COLOR }}></div>
            <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: SECONDARY_COLOR, animationDelay: '0.5s' }}></div>
          </div>
        </div>
      </div>

      {/* Chart content */}
      <div className="p-6 pt-4 relative">
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,143,0,0.1)_1px,transparent_1px)] bg-[length:20px_20px] animate-pulse"></div>

        <div
          style={{ height: `${height}px` }}
          className="w-full relative"
        >
          {children}
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex-1 min-h-screen" style={{ backgroundColor: '#fef7ed' }}>

      
      {/* Enhanced Header with new orange gradient and animations */}
      <div className="mb-8 p-6 rounded-2xl text-white shadow-large animate-fade-in relative overflow-hidden"
           style={{ background: `linear-gradient(135deg, ${PRIMARY_COLOR} 0%, ${SECONDARY_COLOR} 50%, #e65100 100%)` }}>

        {/* Animated background elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -translate-y-16 translate-x-16 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-5 rounded-full translate-y-12 -translate-x-12 animate-bounce" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-white opacity-5 rounded-full -translate-x-1/2 -translate-y-1/2 animate-spin" style={{ animationDuration: '20s' }}></div>

        <div className="relative z-10">
          <h1 className="text-3xl font-bold mb-2 animate-slide-up">
{dashboardData.tenant_info?.name} {t('dashboard', 'Dashboard')}
          </h1>
          <p className="text-white/80 text-lg animate-fade-in" style={{ animationDelay: '0.3s' }}>
            Last updated: {new Date(dashboardData.last_updated).toLocaleString()}
          </p>

          {/* Status indicator */}
          <div className="mt-4 flex items-center animate-slide-up" style={{ animationDelay: '0.5s' }}>
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse mr-2"></div>
            <span className="text-white/80 text-sm">Live Data</span>
          </div>
        </div>
      </div>

      <div className="space-y-8">
        {/* Row 1: Big Number Cards - 3 Columns */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 animate-slide-up">
          <BigNumberCard
            title={t('total_citizens', 'Total Citizens')}
            value={dashboardData.total_citizens?.count}
            icon={<PeopleIcon className="w-12 h-12" />}
            color="primary"
          />

          <BigNumberCard
            title={t('new_registrations', 'New This Month')}
            value={dashboardData.new_registrations_this_month?.count || 0}
            icon={<PersonAddIcon className="w-12 h-12" />}
            color="success"
          />

          <BigNumberCard
            title={t('expiring_soon', 'Expiring Soon')}
            value={dashboardData.expiring_ids_next_30_days?.count || 0}
            icon={<WarningIcon className="w-12 h-12" />}
            color="warning"
          />
        </div>

        {/* Row 1.5: Additional Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 animate-slide-up" style={{ animationDelay: '0.1s' }}>
          <BigNumberCard
            title={t('expired_ids', 'Expired IDs')}
            value={dashboardData.expired_ids_over_30_days || 0}
            icon={<ErrorIcon className="w-12 h-12" />}
            color="error"
          />

          <BigNumberCard
            title={t('id_status_active', 'ID Status Active')}
            value={dashboardData.id_status_summary?.find(item => item.status === 'active')?.count || 0}
            icon={<BadgeIcon className="w-12 h-12" />}
            color="success"
          />

          <BigNumberCard
            title={t('migration_balance', 'Migration Balance')}
            value={dashboardData.migration_data?.net_migration || 0}
            icon={<TrendingUpIcon className="w-12 h-12" />}
            color={dashboardData.migration_data?.net_migration >= 0 ? "success" : "error"}
          />
        </div>

        {/* Row 2: Registration Trend */}
        <div className="animate-slide-up" style={{ animationDelay: '0.2s' }}>
          <ChartCard
            title="Registration Trend (Last 12 Months)"
            height={400}
            icon={<TimelineIcon />}
            gradient={true}
          >
            <ResponsiveContainer width="100%" height={340}>
              <LineChart
                data={dashboardData.total_citizens?.trend || []}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <defs>
                  <linearGradient id="colorRegistrations" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.05}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                <XAxis
                  dataKey="month"
                  stroke="#64748b"
                  fontSize={12}
                  tick={{ fill: '#64748b' }}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <YAxis
                  stroke="#64748b"
                  fontSize={12}
                  tick={{ fill: '#64748b' }}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    backdropFilter: 'blur(10px)'
                  }}
                  labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                />
                <Area
                  type="monotone"
                  dataKey="count"
                  stroke="#3b82f6"
                  fillOpacity={1}
                  fill="url(#colorRegistrations)"
                />
                <Line
                  type="monotone"
                  dataKey="count"
                  stroke="#3b82f6"
                  strokeWidth={4}
                  name="New Registrations"
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 5, stroke: '#ffffff' }}
                  activeDot={{ r: 8, stroke: '#3b82f6', strokeWidth: 3, fill: '#ffffff' }}
                />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-4 text-center">
              <div
                className="inline-flex items-center px-4 py-2 rounded-full animate-pulse"
                style={{ backgroundColor: `${PRIMARY_COLOR}10` }}
              >
                <div className="w-3 h-3 rounded-full mr-2 animate-ping" style={{ backgroundColor: PRIMARY_COLOR }}></div>
                <span className="text-sm font-medium" style={{ color: SECONDARY_COLOR }}>Monthly Registration Trend</span>
              </div>
            </div>
          </ChartCard>
        </div>

        {/* Row 3: Population by Ketena and Gender Ratio */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 animate-slide-up" style={{ animationDelay: '0.2s' }}>
          <div className="lg:col-span-2">
            <ChartCard
              title="Population by Ketena"
              height={400}
              icon={<LocationIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={340}>
                <BarChart
                  data={dashboardData.population_by_ketena || []}
                  layout="horizontal"
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <defs>
                    <linearGradient id="colorKetena" x1="0" y1="0" x2="1" y2="0">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#1d4ed8" stopOpacity={0.9}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                  <XAxis
                    type="number"
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <YAxis
                    dataKey="ketena"
                    type="category"
                    width={80}
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                  <Bar
                    dataKey="count"
                    fill="url(#colorKetena)"
                    radius={[0, 8, 8, 0]}
                    stroke="#1d4ed8"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
              <div className="mt-4 text-center">
                <div className="inline-flex items-center px-4 py-2 bg-blue-50 rounded-full animate-pulse">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-2 animate-ping"></div>
                  <span className="text-sm font-medium text-blue-700">Population by Administrative Area</span>
                </div>
              </div>
            </ChartCard>
          </div>

          <div>
            <ChartCard
              title="Gender Distribution"
              height={400}
              icon={<PeopleIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={280}>
                <PieChart>
                  <defs>
                    {COLORS.map((color, index) => (
                      <linearGradient key={index} id={`genderGradient${index}`} x1="0" y1="0" x2="1" y2="1">
                        <stop offset="0%" stopColor={color} stopOpacity={0.8}/>
                        <stop offset="100%" stopColor={color} stopOpacity={1}/>
                      </linearGradient>
                    ))}
                  </defs>
                  <Pie
                    data={dashboardData.gender_ratio || []}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                    outerRadius={90}
                    innerRadius={30}
                    paddingAngle={2}
                    dataKey="count"
                    nameKey="gender"
                  >
                    {(dashboardData.gender_ratio || []).map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`url(#genderGradient${index % COLORS.length})`}
                        stroke="#ffffff"
                        strokeWidth={2}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                </PieChart>
              </ResponsiveContainer>
              <div className="mt-4 flex flex-wrap justify-center gap-3">
                {(dashboardData.gender_ratio || []).map((entry, index) => (
                  <div key={entry.gender} className="flex items-center bg-gray-50 px-3 py-2 rounded-full">
                    <div
                      className="w-4 h-4 rounded-full mr-2 shadow-sm"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-sm font-medium text-gray-700">{entry.gender}</span>
                  </div>
                ))}
              </div>
            </ChartCard>
          </div>
        </div>

        {/* Row 4: Age Groups and Blood Type */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-slide-up" style={{ animationDelay: '0.4s' }}>
          <div>
            <ChartCard
              title="Age Group Distribution"
              height={400}
              icon={<PeopleIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={340}>
                <BarChart
                  data={dashboardData.age_group_distribution || []}
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <defs>
                    <linearGradient id="colorAge" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#059669" stopOpacity={0.9}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                  <XAxis
                    dataKey="age_group"
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <YAxis
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                  <Bar
                    dataKey="count"
                    fill="url(#colorAge)"
                    radius={[8, 8, 0, 0]}
                    stroke="#059669"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
              <div className="mt-4 text-center">
                <div className="inline-flex items-center px-4 py-2 bg-green-50 rounded-full">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-sm font-medium text-green-700">Age Demographics</span>
                </div>
              </div>
            </ChartCard>
          </div>

          <div>
            <ChartCard
              title="Blood Type Distribution"
              height={400}
              icon={<HeartIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={280}>
                <PieChart>
                  <defs>
                    {COLORS.map((color, index) => (
                      <linearGradient key={index} id={`bloodGradient${index}`} x1="0" y1="0" x2="1" y2="1">
                        <stop offset="0%" stopColor={color} stopOpacity={0.8}/>
                        <stop offset="100%" stopColor={color} stopOpacity={1}/>
                      </linearGradient>
                    ))}
                  </defs>
                  <Pie
                    data={dashboardData.blood_type_distribution || []}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={90}
                    paddingAngle={3}
                    dataKey="count"
                    nameKey="blood_type"
                  >
                    {(dashboardData.blood_type_distribution || []).map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`url(#bloodGradient${index % COLORS.length})`}
                        stroke="#ffffff"
                        strokeWidth={2}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                </PieChart>
              </ResponsiveContainer>
              <div className="mt-4 flex flex-wrap justify-center gap-3">
                {(dashboardData.blood_type_distribution || []).map((entry, index) => (
                  <div key={entry.blood_type} className="flex items-center bg-gray-50 px-3 py-2 rounded-full">
                    <div
                      className="w-4 h-4 rounded-full mr-2 shadow-sm"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-sm font-medium text-gray-700">{entry.blood_type}</span>
                  </div>
                ))}
              </div>
            </ChartCard>
          </div>
        </div>

        {/* Row 5: ID Status and New Registrations Daily */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-slide-up" style={{ animationDelay: '0.5s' }}>
          <div>
            <ChartCard
              title="ID Status Summary"
              height={400}
              icon={<BadgeIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={340}>
                <BarChart
                  data={dashboardData.id_status_summary || []}
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <defs>
                    <linearGradient id="colorStatus" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#7c3aed" stopOpacity={0.9}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                  <XAxis
                    dataKey="status"
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <YAxis
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                  <Bar
                    dataKey="count"
                    fill="url(#colorStatus)"
                    radius={[8, 8, 0, 0]}
                    stroke="#7c3aed"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
              <div className="mt-4 text-center">
                <div className="inline-flex items-center px-4 py-2 bg-purple-50 rounded-full">
                  <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                  <span className="text-sm font-medium text-purple-700">ID Card Status Overview</span>
                </div>
              </div>
            </ChartCard>
          </div>

          <div>
            <ChartCard
              title="New Registrations This Month (Daily)"
              height={400}
              icon={<PersonAddIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={340}>
                <LineChart
                  data={dashboardData.new_registrations_this_month?.daily_trend || []}
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <defs>
                    <linearGradient id="colorDailyRegistrations" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0.05}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                  <XAxis
                    dataKey="day"
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <YAxis
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                  <Area
                    type="monotone"
                    dataKey="count"
                    stroke="#10b981"
                    fillOpacity={1}
                    fill="url(#colorDailyRegistrations)"
                  />
                  <Line
                    type="monotone"
                    dataKey="count"
                    stroke="#10b981"
                    strokeWidth={4}
                    name="New Registrations"
                    dot={{ fill: '#10b981', strokeWidth: 2, r: 5, stroke: '#ffffff' }}
                    activeDot={{ r: 8, stroke: '#10b981', strokeWidth: 3, fill: '#ffffff' }}
                  />
                </LineChart>
              </ResponsiveContainer>
              <div className="mt-4 text-center">
                <div className="inline-flex items-center px-4 py-2 bg-green-50 rounded-full">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-sm font-medium text-green-700">Daily Registration Activity</span>
                </div>
              </div>
            </ChartCard>
          </div>
        </div>

        {/* Row 6: Expiring IDs and Migration */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-slide-up" style={{ animationDelay: '0.6s' }}>
          <div>
            <ChartCard
              title="Expiring IDs in Next 30 Days"
              height={400}
              icon={<WarningIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={340}>
                <AreaChart
                  data={dashboardData.expiring_ids_next_30_days?.daily_trend || []}
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <defs>
                    <linearGradient id="colorExpiring" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                  <XAxis
                    dataKey="day"
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <YAxis
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                  <Area
                    type="monotone"
                    dataKey="count"
                    stroke="#ef4444"
                    fill="url(#colorExpiring)"
                    strokeWidth={3}
                    name="Expiring IDs"
                  />
                </AreaChart>
              </ResponsiveContainer>
              <div className="mt-4 text-center">
                <div className="inline-flex items-center px-4 py-2 bg-red-50 rounded-full">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  <span className="text-sm font-medium text-red-700">Expiration Alert Timeline</span>
                </div>
              </div>
            </ChartCard>
          </div>

          <div>
            <ChartCard
              title="Migration Summary"
              height={400}
              icon={<MigrationIcon />}
              gradient={true}
            >
              <ResponsiveContainer width="100%" height={340}>
                <BarChart
                  data={[
                    { type: 'Transfers In', count: dashboardData.migration_data?.transfers_in || 0 },
                    { type: 'Transfers Out', count: dashboardData.migration_data?.transfers_out || 0 },
                    { type: 'Clearances', count: dashboardData.migration_data?.clearances_issued || 0 },
                    { type: 'Net Migration', count: dashboardData.migration_data?.net_migration || 0 }
                  ]}
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <defs>
                    <linearGradient id="colorMigration" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#d97706" stopOpacity={0.9}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.5} />
                  <XAxis
                    dataKey="type"
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <YAxis
                    stroke="#64748b"
                    fontSize={12}
                    tick={{ fill: '#64748b' }}
                    axisLine={{ stroke: '#e2e8f0' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                      backdropFilter: 'blur(10px)'
                    }}
                    labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                  />
                  <Bar
                    dataKey="count"
                    fill="url(#colorMigration)"
                    radius={[8, 8, 0, 0]}
                    stroke="#d97706"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
              <div className="mt-4 text-center">
                <div className="inline-flex items-center px-4 py-2 bg-yellow-50 rounded-full">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                  <span className="text-sm font-medium text-yellow-700">Population Movement Analytics</span>
                </div>
              </div>
            </ChartCard>
          </div>
        </div>

        {/* Row 7: Disability Status */}
        <div className="animate-slide-up" style={{ animationDelay: '0.7s' }}>
          <ChartCard
            title="Disability Status Distribution"
            height={400}
            icon={<DisabilityIcon />}
            gradient={true}
          >
            <ResponsiveContainer width="100%" height={280}>
              <PieChart>
                <defs>
                  {COLORS.map((color, index) => (
                    <linearGradient key={index} id={`disabilityGradient${index}`} x1="0" y1="0" x2="1" y2="1">
                      <stop offset="0%" stopColor={color} stopOpacity={0.8}/>
                      <stop offset="100%" stopColor={color} stopOpacity={1}/>
                    </linearGradient>
                  ))}
                </defs>
                <Pie
                  data={dashboardData.disability_status || []}
                  cx="50%"
                  cy="50%"
                  innerRadius={50}
                  outerRadius={100}
                  paddingAngle={3}
                  dataKey="count"
                  nameKey="disability_status"
                >
                  {(dashboardData.disability_status || []).map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#disabilityGradient${index % COLORS.length})`}
                      stroke="#ffffff"
                      strokeWidth={2}
                    />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                    backdropFilter: 'blur(10px)'
                  }}
                  labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 flex flex-wrap justify-center gap-3">
              {(dashboardData.disability_status || []).map((entry, index) => (
                <div key={entry.disability_status} className="flex items-center bg-gray-50 px-3 py-2 rounded-full">
                  <div
                    className="w-4 h-4 rounded-full mr-2 shadow-sm"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700">{entry.disability_status}</span>
                </div>
              ))}
            </div>
          </ChartCard>
        </div>
      </div>
    </div>
  );
};

export default KebeleDashboard;
