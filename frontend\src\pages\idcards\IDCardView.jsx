import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  CircularProgress,
  Divider,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  Print as PrintIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  ArrowBack as BackIcon,
  CreditCard as FrontIcon,
  CreditCardOff as BackSideIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import { canApproveIDCards } from '../../utils/permissions';
import { usePermissions } from '../../hooks/usePermissions';
import { getSecurityPatternClass, getPatternDescription } from '../../utils/securityPatterns';
import { useLocalization } from '../../contexts/LocalizationContext';
import IDCardTemplate from '../../components/idcards/IDCardTemplate';
import DirectPrintHandler from '../../components/idcards/DirectPrintHandler';
import OneClickPrint from '../../components/idcards/OneClickPrint';
import '../../styles/securityPatterns.css';

// No more mock data - using real API calls

/**
 * Simple QR Code component using QR Server API
 */
const QRCode = ({ value, size = 80 }) => {
  const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(value)}`;

  return (
    <img
      src={qrUrl}
      alt="QR Code"
      style={{
        width: size,
        height: size,
        border: '2px solid #ccc',
        borderRadius: 4
      }}
      onError={(e) => {
        // Fallback to placeholder if QR service fails
        e.target.style.display = 'none';
        e.target.nextSibling.style.display = 'flex';
      }}
    />
  );
};

/**
 * Get tenant ID from multiple sources for reliability
 */
const getTenantId = () => {
  console.log('🔍 Getting tenant ID...');

  // First try to get from localStorage user object
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.tenant_id) {
      console.log('🔍 From localStorage user:', user.tenant_id);
      return user.tenant_id;
    }
  } catch (e) {
    console.warn('Could not parse stored user data');
  }

  // Fallback: Get from JWT token
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      const base64Url = accessToken.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      const tokenData = JSON.parse(jsonPayload);
      console.log('🔍 From JWT token:', tokenData.tenant_id);
      return tokenData.tenant_id;
    }
  } catch (e) {
    console.warn('Could not decode JWT token');
  }

  console.log('❌ No tenant ID found');
  return null;
};

const IDCardView = () => {
  const { id, tenantId } = useParams(); // Support both /idcards/:id and /tenants/:tenantId/idcards/:id
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useAuth();
  const { t } = useLocalization();
  const { hasPermission } = usePermissions();
  const [idCard, setIdCard] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [comment, setComment] = useState('');
  const [showFront, setShowFront] = useState(true); // Toggle between front and back

  // Auto-open print preview if 'print' parameter is present
  useEffect(() => {
    if (searchParams.get('print') === 'true' && idCard && hasPermission('print_idcards')) {
      setPrintPreviewOpen(true);
      // Remove the print parameter from URL
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.delete('print');
        return newParams;
      });
    }
  }, [idCard, searchParams, hasPermission, setSearchParams]);

  useEffect(() => {
    const fetchIDCard = async () => {
      try {
        setLoading(true);
        setError('');

        console.log('🔍 Fetching ID card with ID:', id);

        // Use tenant ID from URL params if available (cross-tenant navigation), otherwise use current user's tenant
        const effectiveTenantId = tenantId || getTenantId();

        if (!effectiveTenantId) {
          throw new Error('No tenant ID found. Please log in again.');
        }

        console.log('🔍 Using tenant ID:', effectiveTenantId);
        console.log('🔍 Cross-tenant context (tenantId from URL):', tenantId);

        let response;
        let idCardData;

        // If we have tenantId from URL params, we're in cross-tenant context
        if (tenantId) {
          console.log('🔄 Using cross-tenant detail endpoint for subcity admin access');
          try {
            // Try cross-tenant detail endpoint first
            response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/cross_tenant_detail/`);
            idCardData = response.data;
            console.log('✅ ID card fetched via cross-tenant endpoint');
          } catch (crossTenantError) {
            console.warn('⚠️ Cross-tenant endpoint failed, trying regular endpoint:', crossTenantError);
            // Fallback to regular endpoint
            response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/`);
            idCardData = response.data;
          }
        } else {
          console.log('🔄 Using regular tenant-specific endpoint');
          // Regular tenant-specific endpoint for same-tenant access
          response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/`);
          idCardData = response.data;
        }

        console.log('✅ ID card fetched successfully:', idCardData);
        console.log('🔍 ID card citizen info:', {
          citizen_id: idCardData.citizen_id,
          citizen_name: idCardData.citizen_name,
          citizen_digital_id: idCardData.citizen_digital_id,
          card_number: idCardData.card_number
        });

        // If we have a citizen_id, fetch additional citizen details for photo and other info
        if (idCardData.citizen_id && !idCardData.citizen_details) {
          try {
            console.log('🔍 Fetching additional citizen details for ID:', idCardData.citizen_id);

            let citizenData;
            if (tenantId && idCardData.kebele_tenant) {
              // For cross-tenant context, use the kebele tenant ID where the citizen is stored
              console.log('🔄 Fetching citizen from kebele tenant:', idCardData.kebele_tenant.id);
              const citizenResponse = await axios.get(`/api/tenants/${idCardData.kebele_tenant.id}/citizens/${idCardData.citizen_id}/`);
              citizenData = citizenResponse.data;
            } else {
              // Regular same-tenant access
              const citizenResponse = await axios.get(`/api/tenants/${effectiveTenantId}/citizens/${idCardData.citizen_id}/`);
              citizenData = citizenResponse.data;
            }

            // Fetch family data (emergency contacts) for the citizen
            let familyData = {};
            try {
              const emergencyContactsResponse = await axios.get(`/api/tenants/${tenantId && idCardData.kebele_tenant ? idCardData.kebele_tenant.id : effectiveTenantId}/citizens/${idCardData.citizen_id}/emergency-contacts/`);
              familyData.emergencyContacts = emergencyContactsResponse.data;
              console.log('✅ Emergency contacts fetched:', emergencyContactsResponse.data);
            } catch (emergencyError) {
              console.warn('⚠️ Could not fetch emergency contacts:', emergencyError);
              familyData.emergencyContacts = [];
            }

            // Merge citizen data with ID card data
            const enhancedIdCard = {
              ...idCardData,
              citizen: citizenData, // Include full citizen data
              familyData: familyData, // Include family data for emergency contacts
              citizen_photo: citizenData.photo,
              citizen_gender: citizenData.gender,
              citizen_date_of_birth: citizenData.date_of_birth,
              citizen_place_of_birth: citizenData.place_of_birth || citizenData.subcity || citizenData.kebele,
              citizen_blood_type: citizenData.blood_type,
              // Add Amharic names
              citizen_first_name_am: citizenData.first_name_am,
              citizen_middle_name_am: citizenData.middle_name_am,
              citizen_last_name_am: citizenData.last_name_am,
              // Add English names for fallback
              citizen_first_name: citizenData.first_name,
              citizen_middle_name: citizenData.middle_name,
              citizen_last_name: citizenData.last_name
            };

            // Get tenant hierarchy for dynamic header
            console.log('🔍 Getting tenant hierarchy...');

            // Try to get from user context first
            let tenantHierarchy = {
              current_tenant: 'ገብርኤል',
              parent_tenant: 'ዞብል',
              city_tenant: 'ጎንደር'
            };

            if (user?.tenant_name) {
              tenantHierarchy.current_tenant = user.tenant_name;
            }
            if (user?.parent_tenant_name) {
              tenantHierarchy.parent_tenant = user.parent_tenant_name;
            }
            if (user?.city_tenant_name) {
              tenantHierarchy.city_tenant = user.city_tenant_name;
            }

            // If not available in user context, try to fetch from API
            if (!user?.tenant_name) {
              try {
                console.log('🔍 Fetching tenant info from API...');
                const tenantResponse = await axios.get(`/api/tenants/${effectiveTenantId}/info/`);
                const tenantData = tenantResponse.data;

                tenantHierarchy = {
                  current_tenant: tenantData.name || tenantHierarchy.current_tenant,
                  parent_tenant: tenantData.parent_name || tenantHierarchy.parent_tenant,
                  city_tenant: tenantData.city_name || tenantHierarchy.city_tenant
                };

                console.log('✅ Tenant hierarchy from API:', tenantHierarchy);
              } catch (hierarchyError) {
                console.warn('⚠️ Could not fetch tenant hierarchy, using fallbacks:', hierarchyError);
              }
            } else {
              console.log('✅ Tenant hierarchy from user context:', tenantHierarchy);
            }

            enhancedIdCard.tenant_hierarchy = tenantHierarchy;

            console.log('✅ Enhanced ID card with citizen details:', enhancedIdCard);
            console.log('🔒 Security pattern status:', {
              has_kebele_pattern: enhancedIdCard.has_kebele_pattern,
              has_subcity_pattern: enhancedIdCard.has_subcity_pattern,
              status: enhancedIdCard.status,
              pattern_class: getSecurityPatternClass(enhancedIdCard, user)
            });
            setIdCard(enhancedIdCard);
          } catch (citizenError) {
            console.warn('⚠️ Could not fetch additional citizen details:', citizenError);
            // Still set the basic ID card data
            console.log('🔒 Basic ID card security pattern status:', {
              has_kebele_pattern: idCardData.has_kebele_pattern,
              has_subcity_pattern: idCardData.has_subcity_pattern,
              status: idCardData.status,
              pattern_class: getSecurityPatternClass(idCardData, user)
            });
            setIdCard(idCardData);
          }
        } else if (idCardData.citizen_details) {
          // Cross-tenant endpoint already provided citizen details
          console.log('✅ Using citizen details from cross-tenant endpoint');

          // Fetch family data (emergency contacts) for cross-tenant case
          let familyData = {};
          try {
            const emergencyContactsResponse = await axios.get(`/api/tenants/${idCardData.kebele_tenant.id}/citizens/${idCardData.citizen_id}/emergency-contacts/`);
            familyData.emergencyContacts = emergencyContactsResponse.data;
            console.log('✅ Emergency contacts fetched (cross-tenant):', emergencyContactsResponse.data);
          } catch (emergencyError) {
            console.warn('⚠️ Could not fetch emergency contacts (cross-tenant):', emergencyError);
            familyData.emergencyContacts = [];
          }

          const enhancedIdCard = {
            ...idCardData,
            citizen: idCardData.citizen_details,
            familyData: familyData, // Include family data for emergency contacts
            tenant_hierarchy: {
              current_tenant: idCardData.kebele_tenant?.name || user?.tenant_name || 'ገብርኤል',
              parent_tenant: idCardData.kebele_tenant?.parent_name || user?.parent_tenant_name || 'ዞብል',
              city_tenant: user?.city_tenant_name || 'ጎንደር'
            }
          };
          console.log('🔒 Enhanced ID card (with cross-tenant citizen) security pattern status:', {
            has_kebele_pattern: enhancedIdCard.has_kebele_pattern,
            has_subcity_pattern: enhancedIdCard.has_subcity_pattern,
            status: enhancedIdCard.status,
            pattern_class: getSecurityPatternClass(enhancedIdCard, user)
          });
          setIdCard(enhancedIdCard);
        } else {
          // Add tenant hierarchy even for basic ID card data
          const basicIdCard = {
            ...idCardData,
            tenant_hierarchy: {
              current_tenant: user?.tenant_name || 'ገብርኤል',
              parent_tenant: user?.parent_tenant_name || 'ዞብል',
              city_tenant: user?.city_tenant_name || 'ጎንደር'
            }
          };
          console.log('🔒 Basic ID card (no citizen) security pattern status:', {
            has_kebele_pattern: basicIdCard.has_kebele_pattern,
            has_subcity_pattern: basicIdCard.has_subcity_pattern,
            status: basicIdCard.status,
            pattern_class: getSecurityPatternClass(basicIdCard, user)
          });
          setIdCard(basicIdCard);
        }

      } catch (error) {
        console.error('❌ Error fetching ID card:', error);
        setError(error.response?.data?.detail || error.message || 'Failed to fetch ID card');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchIDCard();
    }
  }, [id]);

  const [printPreviewOpen, setPrintPreviewOpen] = useState(false);
  const [cardSide, setCardSide] = useState('front');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [directPrinting, setDirectPrinting] = useState(false);

  // Helper function to get reliable tenant ID
  const getReliableTenantId = () => {
    return tenantId || getTenantId();
  };

  const handlePrintPreview = () => {
    // Open print preview modal
    setPrintPreviewOpen(true);
  };

  const handleDirectPrintComplete = async () => {
    try {
      setDirectPrinting(true);

      // Mark ID card as printed in the backend
      const effectiveTenantId = getReliableTenantId();
      const printTenantId = idCard.kebele_tenant?.id || effectiveTenantId;

      await axios.post(`/api/tenants/${printTenantId}/idcards/${id}/print/`);

      // Refresh the ID card data to show updated status
      fetchIDCard();

      // Show success message
      setSnackbar({
        open: true,
        message: 'ID card sent to printer successfully!',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error marking ID card as printed:', error);
      setSnackbar({
        open: true,
        message: 'Print completed, but failed to update status.',
        severity: 'warning'
      });
    } finally {
      setDirectPrinting(false);
    }
  };

  const handleDirectPrintError = (error) => {
    setDirectPrinting(false);
    setSnackbar({
      open: true,
      message: `Print failed: ${error}`,
      severity: 'error'
    });
  };

  const handleApproveDialogOpen = () => {
    setApproveDialogOpen(true);
  };

  const handleApproveDialogClose = () => {
    setApproveDialogOpen(false);
  };

  const handleRejectDialogOpen = () => {
    setRejectDialogOpen(true);
  };

  const handleRejectDialogClose = () => {
    setRejectDialogOpen(false);
  };

  const handleSubmitForApproval = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Submitting ID card for approval:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email,
        userIsSuperuser: user?.is_superuser
      });

      await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'submit_for_approval',
        comment: 'Submitted for kebele leader approval'
      });

      // Refresh the ID card data
      window.location.reload();
    } catch (error) {
      console.error('Error submitting for approval:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to submit for approval';
      setError(`Failed to submit for approval: ${errorMessage}`);
    }
  };

  const handleKebeleApprove = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Kebele approving ID card:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email
      });

      const response = await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'kebele_approve',
        comment: comment
      });

      console.log('✅ Kebele approval response:', response.data);

      setApproveDialogOpen(false);
      setComment('');

      // Update the ID card state with the response data
      if (response.data) {
        console.log('🔄 Updating ID card state with new data:', {
          old_has_kebele_pattern: idCard.has_kebele_pattern,
          new_has_kebele_pattern: response.data.has_kebele_pattern,
          old_status: idCard.status,
          new_status: response.data.status
        });
        setIdCard(response.data);
      } else {
        // Fallback: fetch fresh data
        console.log('🔄 No response data, fetching fresh ID card data...');
        fetchIDCard();
      }
    } catch (error) {
      console.error('Error approving ID card at kebele level:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to approve ID card';
      setError(`Failed to approve ID card: ${errorMessage}`);
    }
  };

  const handleSubcityApprove = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Subcity approving ID card:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email
      });

      // Check authentication status
      const accessToken = localStorage.getItem('accessToken');
      if (accessToken) {
        try {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          console.log('🔍 Token data for subcity approval:', {
            role: tokenData.role,
            tenant_id: tokenData.tenant_id,
            user_id: tokenData.user_id,
            exp: new Date(tokenData.exp * 1000),
            isExpired: tokenData.exp < Math.floor(Date.now() / 1000)
          });
        } catch (e) {
          console.warn('Could not decode token:', e);
        }
      }

      const response = await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'subcity_approve',
        comment: comment
      });

      console.log('✅ Subcity approval response:', response.data);

      setApproveDialogOpen(false);
      setComment('');

      // Update the ID card state with the response data
      if (response.data) {
        console.log('🔄 Updating ID card state with new data:', {
          old_has_subcity_pattern: idCard.has_subcity_pattern,
          new_has_subcity_pattern: response.data.has_subcity_pattern,
          old_status: idCard.status,
          new_status: response.data.status
        });
        setIdCard(response.data);
      } else {
        // Fallback: fetch fresh data
        console.log('🔄 No response data, fetching fresh ID card data...');
        fetchIDCard();
      }
    } catch (error) {
      console.error('Error approving ID card at subcity level:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to approve ID card';
      setError(`Failed to approve ID card: ${errorMessage}`);
    }
  };

  const handleReject = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Rejecting ID card:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email
      });

      await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'reject',
        comment: comment
      });

      setRejectDialogOpen(false);
      setComment('');
      // Refresh the ID card data
      fetchIDCard();
    } catch (error) {
      console.error('Error rejecting ID card:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to reject ID card';
      setError(`Failed to reject ID card: ${errorMessage}`);
    }
  };

  const handleResetToDraft = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Resetting ID card to draft:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email
      });

      await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'reset_to_draft',
        comment: 'Reset to draft for resubmission after addressing rejection feedback'
      });

      // Refresh the ID card data
      window.location.reload();
    } catch (error) {
      console.error('Error resetting ID card to draft:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to reset ID card to draft';
      setError(`Failed to reset ID card to draft: ${errorMessage}`);
    }
  };

  const getStatusChip = (status) => {
    switch (status) {
      case 'approved':
        return <Chip label={t('fully_approved', 'Fully Approved - Ready for Printing')} color="success" />;
      case 'kebele_approved':
        return <Chip label={t('kebele_approved', 'Kebele Approved - Pending Subcity Approval')} color="warning" />;
      case 'pending_approval':
        return <Chip label={t('pending_kebele_approval', 'Pending Kebele Approval')} color="warning" />;
      case 'rejected':
        return <Chip label={t('rejected', 'Rejected')} color="error" />;
      case 'draft':
        return <Chip label={t('draft', 'Draft')} color="default" />;
      case 'printed':
        return <Chip label={t('printed', 'Printed')} color="info" />;
      case 'issued':
        return <Chip label={t('issued', 'Issued')} color="primary" />;
      default:
        return <Chip label={status} />;
    }
  };

  const getWorkflowSteps = () => {
    const steps = [
      { label: t('draft', 'Draft'), completed: true },
      { label: t('kebele_approval', 'Kebele Approval'), completed: ['kebele_approved', 'approved', 'printed', 'issued'].includes(idCard.status) },
      { label: t('subcity_approval', 'Subcity Approval'), completed: ['approved', 'printed', 'issued'].includes(idCard.status) },
      { label: t('ready_for_printing', 'Ready for Printing'), completed: ['approved', 'printed', 'issued'].includes(idCard.status) },
      { label: t('printed', 'Printed & Issued'), completed: ['printed', 'issued'].includes(idCard.status) }
    ];

    return steps;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography variant="h5" color="error" gutterBottom>
          {t('error', 'Error Loading ID Card')}
        </Typography>
        <Typography variant="body1">{error}</Typography>
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => {
            // If we came from cross-tenant navigation, go back to appropriate list
            if (tenantId) {
              navigate('/idcards/all-kebeles');
            } else {
              navigate('/idcards');
            }
          }}
          sx={{ mt: 3 }}
        >
          {t('back', 'Back to ID Cards')}
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => {
              // If we came from cross-tenant navigation, go back to appropriate list
              if (tenantId) {
                navigate('/idcards/all-kebeles');
              } else {
                navigate('/idcards');
              }
            }}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            ID Card Details
          </Typography>
        </Box>
        <Box>
          {idCard.status === 'draft' && user?.role === 'clerk' && (
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmitForApproval}
              sx={{ mr: 1 }}
            >
              {t('submit_for_approval', 'Send for Approval')}
            </Button>
          )}
          {idCard.status === 'pending_approval' && user?.role === 'kebele_leader' && (
            <>
              <Button
                variant="contained"
                color="success"
                startIcon={<ApproveIcon />}
                onClick={handleApproveDialogOpen}
                sx={{ mr: 1 }}
              >
                {t('kebele_approve', 'Kebele Approve')}
              </Button>
              <Button
                variant="contained"
                color="error"
                startIcon={<RejectIcon />}
                onClick={handleRejectDialogOpen}
                sx={{ mr: 1 }}
              >
                {t('reject', 'Reject')}
              </Button>
            </>
          )}
          {idCard.status === 'kebele_approved' && user?.role === 'subcity_admin' && (
            <>
              <Button
                variant="contained"
                color="success"
                startIcon={<ApproveIcon />}
                onClick={handleApproveDialogOpen}
                sx={{ mr: 1 }}
              >
                Subcity Approve
              </Button>
              <Button
                variant="contained"
                color="error"
                startIcon={<RejectIcon />}
                onClick={handleRejectDialogOpen}
                sx={{ mr: 1 }}
              >
                Reject
              </Button>
            </>
          )}
          {idCard.status === 'approved' && hasPermission('print_idcards') && (
            <>
              <Button
                variant="contained"
                color="secondary"
                startIcon={<PrintIcon />}
                onClick={handlePrintPreview}
              >
                Print Preview
              </Button>
              <OneClickPrint
                onPrintComplete={handleDirectPrintComplete}
                onPrintError={handleDirectPrintError}
                loading={directPrinting}
                disabled={directPrinting}
                buttonText="🖨️ Print Front"
                variant="contained"
                color="primary"
                size="medium"
                printMethod="browser"
              >
                <IDCardTemplate
                  idCard={idCard}
                  side="front"
                  preview={false}
                />
              </OneClickPrint>
              <OneClickPrint
                onPrintComplete={handleDirectPrintComplete}
                onPrintError={handleDirectPrintError}
                loading={directPrinting}
                disabled={directPrinting}
                buttonText="🖨️ Print Back"
                variant="outlined"
                color="primary"
                size="medium"
                printMethod="browser"
              >
                <IDCardTemplate
                  idCard={idCard}
                  side="back"
                  preview={false}
                />
              </OneClickPrint>
              <OneClickPrint
                onPrintComplete={handleDirectPrintComplete}
                onPrintError={handleDirectPrintError}
                loading={directPrinting}
                disabled={directPrinting}
                buttonText="📸 Photo Viewer"
                variant="outlined"
                color="secondary"
                size="medium"
                printMethod="image-window"
              >
                <IDCardTemplate
                  idCard={idCard}
                  side="front"
                  preview={false}
                />
              </OneClickPrint>
            </>
          )}
          {idCard.status === 'rejected' && (user?.role === 'clerk' || user?.is_superuser) && (
            <Button
              variant="contained"
              color="warning"
              startIcon={<RefreshIcon />}
              onClick={handleResetToDraft}
              sx={{ mr: 1 }}
            >
              Reset to Draft
            </Button>
          )}
        </Box>
      </Box>

      {/* Workflow Stepper - Minimized */}
      <Paper sx={{ p: 2, mb: 2, boxShadow: 1 }}>
        <Typography variant="h6" gutterBottom sx={{ fontSize: '1.1rem', mb: 1 }}>
          ID Card Workflow
        </Typography>
        <Stepper alternativeLabel>
          {getWorkflowSteps().map((step, index) => (
            <Step key={index} active={step.completed}>
              <StepLabel>{step.label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>

      <Grid container spacing={3}>
        {/* ID Card Preview - Minimized Container */}
        <Grid item xs={12} md={8}>
          <Card sx={{ boxShadow: 1 }}>
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.5 }}>
                <Typography variant="h6" sx={{ fontSize: '1.1rem' }}>
                  {t('id_card_preview', 'ID Card Preview')}
                </Typography>
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  <Button
                    variant={showFront ? "contained" : "outlined"}
                    size="small"
                    onClick={() => setShowFront(true)}
                    sx={{ minWidth: 70, fontSize: '0.75rem', py: 0.5 }}
                  >
                    {t('front_side', 'Front')}
                  </Button>
                  <Button
                    variant={!showFront ? "contained" : "outlined"}
                    size="small"
                    onClick={() => setShowFront(false)}
                    sx={{ minWidth: 70, fontSize: '0.75rem', py: 0.5 }}
                  >
                    {t('back_side', 'Back')}
                  </Button>
                </Box>
              </Box>
              <Divider sx={{ mb: 2 }} />



              {/* Enhanced Ethiopian Digital ID Card Template */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  mb: 2
                }}
              >
                <IDCardTemplate
                  idCard={idCard}
                  side={showFront ? 'front' : 'back'}
                  preview={true}
                />
              </Box>

            </CardContent>
          </Card>
        </Grid>

        {/* ID Card Details - Minimized Container */}
        <Grid item xs={12} md={4}>
          <Card sx={{ boxShadow: 1 }}>
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Typography variant="h6" gutterBottom sx={{ fontSize: '1.1rem', mb: 1 }}>
                {t('id_card_information', 'ID Card Information')}
              </Typography>
              <Divider sx={{ mb: 1.5 }} />

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Status
                  </Typography>
                  <Box sx={{ mb: 1 }}>
                    {getStatusChip(idCard.status)}
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Issue Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.issue_date ? new Date(idCard.issue_date).toLocaleDateString() : 'Not issued yet'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Expiry Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.expiry_date ? new Date(idCard.expiry_date).toLocaleDateString() : 'Not set'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Citizen Information
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Full Name
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.citizen_name || 'Unknown Citizen'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Digital ID
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.citizen_digital_id || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Card Status
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    <Box component="span" sx={{ textTransform: 'capitalize' }}>
                      {idCard.status || 'Unknown'}
                    </Box>
                  </Typography>
                </Grid>

                {/* Security Pattern Information */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    🔒 Security Pattern Status
                  </Typography>
                  <Box sx={{
                    p: 1.5,
                    mt: 1,
                    bgcolor: (idCard.has_kebele_pattern || idCard.has_subcity_pattern) ? 'success.light' : 'grey.100',
                    borderRadius: 1,
                    border: '2px solid',
                    borderColor: (idCard.has_kebele_pattern || idCard.has_subcity_pattern) ? 'success.main' : 'grey.300'
                  }}>
                    <Typography variant="body1" fontWeight="bold" gutterBottom>
                      {getPatternDescription(idCard, user)}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {idCard.has_kebele_pattern && (
                        <Chip
                          label="✅ Kebele Pattern"
                          color="warning"
                          size="small"
                          sx={{ fontWeight: 'bold' }}
                        />
                      )}
                      {idCard.has_subcity_pattern && (
                        <Chip
                          label="✅ Subcity Pattern"
                          color="success"
                          size="small"
                          sx={{ fontWeight: 'bold' }}
                        />
                      )}
                      {!idCard.has_kebele_pattern && !idCard.has_subcity_pattern && (
                        <Chip
                          label="⏳ No Pattern Applied"
                          color="default"
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </Box>
                </Grid>

                {/* Approval Information */}
                {idCard.status === 'approved' && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Approved By
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {idCard.approved_by_username || 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Approved Date
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {idCard.approved_at ? new Date(idCard.approved_at).toLocaleDateString() : 'Unknown'}
                      </Typography>
                    </Grid>
                    {idCard.approval_pattern && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Approval Pattern
                        </Typography>
                        <Typography variant="body1" gutterBottom sx={{ textTransform: 'capitalize' }}>
                          {idCard.approval_pattern.replace('_', ' ')}
                        </Typography>
                      </Grid>
                    )}
                    {idCard.approval_comment && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Approval Comment
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                          {idCard.approval_comment}
                        </Typography>
                      </Grid>
                    )}
                  </>
                )}

                {idCard.status === 'rejected' && idCard.approval_comment && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Rejection Reason
                    </Typography>
                    <Typography variant="body1" gutterBottom color="error">
                      {idCard.approval_comment}
                    </Typography>
                  </Grid>
                )}
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    System Information
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Created By
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.created_by_username || 'System'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Created Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.created_at ? new Date(idCard.created_at).toLocaleDateString() : 'Unknown'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Issue Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.issue_date ? new Date(idCard.issue_date).toLocaleDateString() : 'Not issued yet'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Expiry Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.expiry_date ? new Date(idCard.expiry_date).toLocaleDateString() : 'Not set'}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Approve Dialog */}
      <Dialog open={approveDialogOpen} onClose={handleApproveDialogClose}>
        <DialogTitle>
          {idCard.status === 'pending_approval' ? t('kebele_approval', 'Kebele Approval') : t('subcity_approval', 'Subcity Approval')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {idCard.status === 'pending_approval'
              ? 'Are you sure you want to approve this ID card at kebele level? This will apply the first security pattern and send it to subcity for final approval.'
              : 'Are you sure you want to give final approval to this ID card? This will complete the security pattern and make it ready for printing.'
            }
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="comment"
            label={t('comment_optional', 'Comment (Optional)')}
            type="text"
            fullWidth
            variant="outlined"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleApproveDialogClose}>{t('cancel', 'Cancel')}</Button>
          <Button
            onClick={idCard.status === 'pending_approval' ? handleKebeleApprove : handleSubcityApprove}
            variant="contained"
            color="success"
          >
            {idCard.status === 'pending_approval' ? t('kebele_approve', 'Kebele Approve') : t('subcity_approve', 'Subcity Approve')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onClose={handleRejectDialogClose}>
        <DialogTitle>Reject ID Card</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to reject this ID card? Please provide a reason for rejection.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="comment"
            label={t('reason_for_rejection', 'Reason for Rejection')}
            type="text"
            fullWidth
            variant="outlined"
            required
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRejectDialogClose}>{t('cancel', 'Cancel')}</Button>
          <Button onClick={handleReject} variant="contained" color="error" disabled={!comment}>
            {t('reject', 'Reject')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Enhanced Print Handler */}
      <DirectPrintHandler
        open={printPreviewOpen}
        onClose={() => setPrintPreviewOpen(false)}
        title={`Print ID Card - ${idCard?.citizen_name || idCard?.citizen?.first_name + ' ' + idCard?.citizen?.last_name || 'ID Card'}`}
        onPrintComplete={async () => {
          try {
            // Mark ID card as printed in the backend
            const effectiveTenantId = getReliableTenantId();
            const printTenantId = idCard.kebele_tenant?.id || effectiveTenantId;

            await axios.post(`/api/tenants/${printTenantId}/idcards/${id}/print/`);

            // Refresh the ID card data to show updated status
            fetchIDCard();

            // Show success message
            setSnackbar({
              open: true,
              message: 'ID card printed successfully!',
              severity: 'success'
            });
          } catch (error) {
            console.error('Error marking ID card as printed:', error);
            setSnackbar({
              open: true,
              message: 'Print completed, but failed to update status.',
              severity: 'warning'
            });
          }
        }}
      >
        {/* Card Side Toggle */}
        <Box display="flex" justifyContent="center" mb={3}>
          <ToggleButtonGroup
            value={cardSide}
            exclusive
            onChange={(event, newSide) => {
              if (newSide !== null) {
                setCardSide(newSide);
              }
            }}
            aria-label="card side"
          >
            <ToggleButton value="front" aria-label="front side">
              <FrontIcon sx={{ mr: 1 }} />
              Front Side
            </ToggleButton>
            <ToggleButton value="back" aria-label="back side">
              <BackSideIcon sx={{ mr: 1 }} />
              Back Side
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>

        {/* ID Card Preview for Printing */}
        {idCard && (
          <IDCardTemplate
            idCard={idCard}
            side={cardSide}
            preview={true}
          />
        )}
      </DirectPrintHandler>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default IDCardView;
