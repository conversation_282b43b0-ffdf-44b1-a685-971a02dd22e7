import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  Chip,
  Paper,
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Person as PersonIcon,
  Home as HomeIcon,
  FamilyRestroom as FamilyIcon,
  Contacts as ContactsIcon,
  PhotoCamera as PhotoIcon,
} from '@mui/icons-material';
import { useSharedData } from '../../../contexts/SharedDataContext';
import localizationService from '../../../services/localizationService';

const ReviewStep = ({ formik }) => {
  const sharedData = useSharedData();
  const {
    countries,
    religions,
    maritalStatuses,
    citizenStatuses,
    subcities,
    kebeles,
    ketenas
  } = sharedData;

  const formatDate = (date) => {
    if (!date) return 'Not provided';
    return new Date(date).toLocaleDateString();
  };

  // Helper function to get name from ID
  const getNameFromId = (id, dataArray, defaultValue = 'Not provided') => {
    if (!id || !dataArray) return defaultValue;
    const item = dataArray.find(item => item.id === parseInt(id));
    return item ? item.name : defaultValue;
  };

  const renderSection = (title, icon, children) => (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1 }}>
            {title}
          </Typography>
        </Box>
        {children}
      </CardContent>
    </Card>
  );

  const renderField = (label, value, fullWidth = false) => (
    <Grid item xs={12} md={fullWidth ? 12 : 6}>
      <Box sx={{ mb: 1 }}>
        <Typography variant="caption" color="text.secondary">
          {label}
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          {value || 'Not provided'}
        </Typography>
      </Box>
    </Grid>
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <CheckIcon sx={{ mr: 1, color: 'success.main' }} />
        <Typography variant="h6" component="h2">
          Review Information
        </Typography>
      </Box>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please review all the information below before submitting the citizen registration.
      </Typography>

      {/* Personal Information */}
      {renderSection(
        'Personal Information',
        <PersonIcon sx={{ color: 'primary.main' }} />,
        <Grid container spacing={2}>
          {renderField('First Name', formik.values.first_name)}
          {renderField('Middle Name', formik.values.middle_name)}
          {renderField('Last Name', formik.values.last_name)}
          {renderField('Date of Birth', formatDate(formik.values.date_of_birth))}
          {renderField('Gender', formik.values.gender)}
          {renderField('Phone Number', formik.values.phone_number)}
          {renderField('Email', formik.values.email)}
          {renderField('Nationality', getNameFromId(formik.values.nationality, countries))}
          {renderField('Citizen Status', getNameFromId(formik.values.citizen_status, citizenStatuses))}
          {renderField('Religion', getNameFromId(formik.values.religion, religions))}
          {renderField('Marital Status', getNameFromId(formik.values.marital_status, maritalStatuses))}
          {renderField('Occupation', formik.values.occupation)}
          {renderField('Blood Type', formik.values.blood_type)}
        </Grid>
      )}

      {/* Address Information */}
      {renderSection(
        'Address Information',
        <HomeIcon sx={{ color: 'primary.main' }} />,
        <Grid container spacing={2}>
          {renderField('SubCity', getNameFromId(formik.values.subcity, subcities))}
          {renderField('Kebele', getNameFromId(formik.values.kebele, kebeles))}
          {renderField('Ketena', getNameFromId(formik.values.ketena, ketenas))}
          {renderField('House Number', formik.values.house_number)}
          {renderField('Street', formik.values.street)}
        </Grid>
      )}

      {/* Employment Information */}
      {(formik.values.employment || formik.values.employee_type || formik.values.organization_name) && renderSection(
        'Employment Information',
        <PersonIcon sx={{ color: 'primary.main' }} />,
        <Grid container spacing={2}>
          {renderField('Employment Status', formik.values.employment)}
          {renderField('Employee Type', formik.values.employee_type)}
          {renderField('Organization Name', formik.values.organization_name)}
          {renderField('Region', formik.values.region)}
        </Grid>
      )}

      {/* Family Information */}
      {(formik.values.has_spouse || formik.values.has_parents || formik.values.has_children) && renderSection(
        'Family Information',
        <FamilyIcon sx={{ color: 'primary.main' }} />,
        <Box>
          {/* Spouse */}
          {formik.values.has_spouse && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Spouse
              </Typography>
              <Paper variant="outlined" sx={{ p: 2 }}>
                {formik.values.is_spouse_resident ? (
                  <Box>
                    <Chip label="Resident" color="primary" size="small" sx={{ mb: 1 }} />
                    <Typography variant="body2">
                      {formik.values.spouse_first_name} {formik.values.spouse_middle_name} {formik.values.spouse_last_name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      ID: {formik.values.spouse_id}
                    </Typography>
                  </Box>
                ) : (
                  <Box>
                    <Chip label="Non-resident" color="secondary" size="small" sx={{ mb: 1 }} />
                    <Typography variant="body2">
                      {formik.values.spouse_first_name} {formik.values.spouse_middle_name} {formik.values.spouse_last_name}
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Box>
          )}

          {/* Parents */}
          {formik.values.has_parents && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Parents
              </Typography>
              <Grid container spacing={2}>
                {/* Father */}
                <Grid item xs={12} md={6}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Father
                    </Typography>
                    {formik.values.is_father_resident ? (
                      <Box>
                        <Chip label="Resident" color="primary" size="small" sx={{ mb: 1 }} />
                        <Typography variant="body2">
                          {formik.values.father_first_name} {formik.values.father_middle_name} {formik.values.father_last_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {formik.values.father_id}
                        </Typography>
                      </Box>
                    ) : (
                      <Box>
                        <Chip label="Non-resident" color="secondary" size="small" sx={{ mb: 1 }} />
                        <Typography variant="body2">
                          {formik.values.father_first_name} {formik.values.father_middle_name} {formik.values.father_last_name}
                        </Typography>
                      </Box>
                    )}
                  </Paper>
                </Grid>

                {/* Mother */}
                <Grid item xs={12} md={6}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Mother
                    </Typography>
                    {formik.values.is_mother_resident ? (
                      <Box>
                        <Chip label="Resident" color="primary" size="small" sx={{ mb: 1 }} />
                        <Typography variant="body2">
                          {formik.values.mother_first_name} {formik.values.mother_middle_name} {formik.values.mother_last_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {formik.values.mother_id}
                        </Typography>
                      </Box>
                    ) : (
                      <Box>
                        <Chip label="Non-resident" color="secondary" size="small" sx={{ mb: 1 }} />
                        <Typography variant="body2">
                          {formik.values.mother_first_name} {formik.values.mother_middle_name} {formik.values.mother_last_name}
                        </Typography>
                      </Box>
                    )}
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Children */}
          {formik.values.has_children && formik.values.children.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Children ({formik.values.children.length})
              </Typography>
              <Grid container spacing={2}>
                {formik.values.children.map((child, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Child {index + 1}
                      </Typography>
                      {child.is_resident ? (
                        <Box>
                          <Chip label="Resident" color="primary" size="small" sx={{ mb: 1 }} />
                          <Typography variant="body2">
                            {child.first_name} {child.middle_name} {child.last_name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {child.child_id}
                          </Typography>
                        </Box>
                      ) : (
                        <Box>
                          <Chip label="Non-resident" color="secondary" size="small" sx={{ mb: 1 }} />
                          <Typography variant="body2">
                            {child.first_name} {child.middle_name} {child.last_name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            DOB: {formatDate(child.date_of_birth)} | Gender: {child.gender}
                          </Typography>
                        </Box>
                      )}
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </Box>
      )}

      {/* Emergency Contact */}
      {renderSection(
        'Emergency Contact',
        <ContactsIcon sx={{ color: 'primary.main' }} />,
        <Paper variant="outlined" sx={{ p: 2 }}>
          {formik.values.is_emergency_contact_resident ? (
            <Box>
              <Chip label="Resident" color="primary" size="small" sx={{ mb: 1 }} />
              <Typography variant="body2">
                {formik.values.emergency_contact_first_name} {formik.values.emergency_contact_middle_name} {formik.values.emergency_contact_last_name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                ID: {formik.values.emergency_contact_id} | Relationship: {formik.values.emergency_contact_relation}
              </Typography>
            </Box>
          ) : (
            <Box>
              <Chip label="Non-resident" color="secondary" size="small" sx={{ mb: 1 }} />
              <Typography variant="body2">
                {formik.values.emergency_contact_first_name} {formik.values.emergency_contact_middle_name} {formik.values.emergency_contact_last_name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Phone: {formik.values.emergency_contact_phone} | Email: {formik.values.emergency_contact_email} | Relationship: {formik.values.emergency_contact_relation}
              </Typography>
            </Box>
          )}
        </Paper>
      )}

      {/* Photo */}
      {renderSection(
        'Photo',
        <PhotoIcon sx={{ color: 'primary.main' }} />,
        <Box sx={{ textAlign: 'center' }}>
          {formik.values.photo ? (
            <Box>
              <img
                src={formik.values.photo}
                alt="Citizen Photo"
                style={{
                  width: '200px',
                  height: '250px',
                  objectFit: 'cover',
                  borderRadius: 8,
                  border: '2px solid #2196f3'
                }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                Photo captured successfully
              </Typography>
            </Box>
          ) : (
            <Box sx={{ py: 4 }}>
              <PhotoIcon sx={{ fontSize: 48, color: '#ccc', mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                No photo provided
              </Typography>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default ReviewStep;
